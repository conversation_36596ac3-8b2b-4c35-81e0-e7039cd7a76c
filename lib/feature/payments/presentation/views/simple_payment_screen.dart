import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';

class SimplePaymentScreen extends StatefulWidget {
  final int reservationId;
  final double amount;
  final String propertyTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final String? customerEmail;

  const SimplePaymentScreen({
    super.key,
    required this.reservationId,
    required this.amount,
    required this.propertyTitle,
    required this.checkIn,
    required this.checkOut,
    this.customerEmail,
  });

  @override
  State<SimplePaymentScreen> createState() => _SimplePaymentScreenState();
}

class _SimplePaymentScreenState extends State<SimplePaymentScreen> {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final formatter = NumberFormat("#,##0.00", "en_US");

    // Check if PaymentCubit is available
    try {
      context.read<PaymentCubit>();
    } catch (e) {
      debugPrint('❌ PaymentCubit not found in widget tree: $e');
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          title: Text(
            s.paymentError,
            style: TextStyle(color: context.primaryTextColor),
          ),
          backgroundColor: context.backgroundColor,
          foregroundColor: context.primaryTextColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                s.paymentServiceUnavailable,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                s.tryAgainLater,
                style: TextStyle(
                  fontSize: 16,
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(
          s.payment,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: context.primaryTextColor,
          ),
        ),
        backgroundColor: context.backgroundColor,
        foregroundColor: context.primaryTextColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: BlocConsumer<PaymentCubit, PaymentState>(
          listener: (context, state) {
            if (state is PaymentSuccess) {
              _showSuccessDialog(context);
            } else if (state is PaymentFailed) {
              _showErrorDialog(context, state.message);
            } else if (state is PaymentCancelled) {
              _showCancelledDialog(context);
            }
          },
          builder: (context, state) {
            debugPrint('🔄 BlocConsumer builder - PaymentState: $state');

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBookingSummary(s, formatter),
                  const SizedBox(height: 24),
                  _buildPaymentMethods(s),
                  const SizedBox(height: 24),
                  _buildSecurityInfo(s),
                  const SizedBox(height: 24),
                  // Show current state for debugging
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Payment State: ${state.runtimeType}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildPayButton(context, state, s),
                  // Add extra padding for bottom navigation
                  const SizedBox(height: 100),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBookingSummary(S s, NumberFormat formatter) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.bookingSummary,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow(s.property, widget.propertyTitle),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.checkIn,
            DateFormat('dd/MM/yyyy').format(widget.checkIn),
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.checkOut,
            DateFormat('dd/MM/yyyy').format(widget.checkOut),
          ),
          const Divider(height: 24),
          _buildSummaryRow(
            s.totalAmount,
            '${formatter.format(widget.amount)} ${s.sar}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: context.primaryTextColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color: isTotal ? context.accentColor : context.primaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethods(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.paymentMethods,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildPaymentMethodsGrid(),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsGrid() {
    final paymentMethods = [
      {'name': 'MADA', 'isSelected': true},
      {'name': 'Visa', 'isSelected': false},
      {'name': 'Mastercard', 'isSelected': false},
      {'name': 'Apple Pay', 'isSelected': false},
      {'name': 'STC Pay', 'isSelected': false},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: paymentMethods.map((method) {
        final isSelected = method['isSelected'] as bool;
        return GestureDetector(
          onTap: () {
            // Handle payment method selection
            debugPrint('Selected payment method: ${method['name']}');
          },
          child: Container(
            width: (MediaQuery.of(context).size.width - 80) / 3,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? context.accentColor
                    : context.borderColor,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected
                  ? context.accentColor.withValues(alpha: 0.1)
                  : context.cardColor,
            ),
            child: Center(
              child: Text(
                method['name'] as String,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? context.primaryTextColor : context.secondaryTextColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSecurityInfo(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.successColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.successColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: context.successColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.securePayment,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: context.successColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  s.paymentSecurityNote,
                  style: TextStyle(
                    fontSize: 14,
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayButton(BuildContext context, PaymentState state, S s) {
    final isLoading = state is PaymentLoading;

    debugPrint('🔄 _buildPayButton - state: $state, isLoading: $isLoading');

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : () => _processPayment(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: context.accentColor,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Text(
                s.payNow,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  void _processPayment(BuildContext context) {
    context.read<PaymentCubit>().processPayment(
      context: context,
      reservationId: widget.reservationId,
      amount: widget.amount,
      customerEmail: widget.customerEmail,
    );
  }

  void _showSuccessDialog(BuildContext context) {
    final s = S.of(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: context.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: context.successColor,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              s.paymentSuccessful,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              s.reservationConfirmed,
              style: TextStyle(
                fontSize: 16,
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Close payment screen
                  Navigator.of(context).pop(); // Close booking screen
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.accentColor,
                  foregroundColor: Colors.black,
                ),
                child: Text(s.done),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Payment Failed',
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.font16Regular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _processPayment(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.yellow,
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Retry'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelledDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.cancel,
              color: Colors.orange,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Payment Cancelled',
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'You cancelled the payment process.',
              style: AppTextStyles.font16Regular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: Colors.black,
                ),
                child: const Text('OK'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
