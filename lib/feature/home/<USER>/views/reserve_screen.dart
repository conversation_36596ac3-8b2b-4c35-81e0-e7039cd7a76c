import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:intl/intl.dart';
import 'booking_confirmation_screen.dart';

class ReserveScreen extends StatefulWidget {
  final int serviceCategoryItemId; // Property ID for API calls
  final String placeTitle;
  final double pricePerNight;
  final String? bookingRules; // Booking rules from API
  final String? cancelationRules; // Legacy cancellation rules
  final CancellationPolicyModel? cancellationPolicy; // New structured policy
  final DateTime? checkInDate;
  final DateTime? checkOutDate;

  const ReserveScreen({
    super.key,
    required this.serviceCategoryItemId,
    required this.placeTitle,
    required this.pricePerNight,
    this.bookingRules,
    this.cancelationRules,
    this.cancellationPolicy,
    this.checkInDate,
    this.checkOutDate,
  });

  @override
  State<ReserveScreen> createState() => _ReserveScreenState();
}

class _ReserveScreenState extends State<ReserveScreen> {
  DateTime? checkInDate;
  DateTime? checkOutDate;
  int adults = 1;
  int children = 0;

  bool agreedToPolicies = false;
  bool isCreatingReservation = false; // Add loading state

  final DateFormat formatter = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    // Initialize with passed dates if available
    checkInDate = widget.checkInDate;
    checkOutDate = widget.checkOutDate;
  }

  @override
  void dispose() {
    // No dialog cleanup needed since we're using button loading state
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    int numberOfNights = 0;
    double totalPrice = 0;

    if (checkInDate != null && checkOutDate != null) {
      numberOfNights = checkOutDate!.difference(checkInDate!).inDays;
      if (numberOfNights < 0) numberOfNights = 0;
      totalPrice = numberOfNights * widget.pricePerNight;
    }

    // Check if dates are valid
    bool datesValid = checkInDate != null &&
                     checkOutDate != null &&
                     checkOutDate!.isAfter(checkInDate!) &&
                     numberOfNights > 0;

    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(s.reserve,
            style: AppTextStyles.font18Bold.copyWith(color: context.primaryTextColor)),
        backgroundColor: context.backgroundColor,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.primaryTextColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ListView(
          children: [
            Text(
              widget.placeTitle,
              style: AppTextStyles.font24Bold.copyWith(color: context.primaryTextColor),
            ),
            const SizedBox(height: 20),

            // اختيار التواريخ
            buildDateTile(s.checkIn, checkInDate, true),
            buildDateTile(s.checkOut, checkOutDate, false),

            const SizedBox(height: 20),
            // Booking Rules and Cancellation Policy
            _buildPoliciesSection(s),
            const SizedBox(height: 20),
            // عدد الأشخاص
            buildCounterRow(
                s.adults, adults, (val) => setState(() => adults = val)),
            buildCounterRow(
                s.children, children, (val) => setState(() => children = val)),

            const SizedBox(height: 20),



            // شروط الحجز ✅
            CheckboxListTile(
              title: Text(
                s.agreeToBookingPolicies,
                style: TextStyle(color: context.primaryTextColor),
              ),
              value: agreedToPolicies,
              onChanged: (value) {
                setState(() {
                  agreedToPolicies = value ?? false;
                });
              },
            ),
            const SizedBox(height: 10),
            if (!agreedToPolicies)
              Text(
                s.mustAgreeToBookingPolicies,
                style: const TextStyle(color: Colors.red, fontSize: 14),
              ),

            // Date validation messages
            if (checkInDate != null && checkOutDate != null && !checkOutDate!.isAfter(checkInDate!))
              Text(
                s.checkOutMustBeAfterCheckIn,
                style: const TextStyle(color: Colors.red, fontSize: 14),
              ),

            if (checkInDate == null || checkOutDate == null)
              Text(
                s.pleaseSelectBothDates,
                style: const TextStyle(color: Colors.red, fontSize: 14),
              ),

            const SizedBox(height: 20),

            // ملخص السعر
            if (numberOfNights > 0)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${s.numberOfNights}: $numberOfNights',
                      style: TextStyle(fontSize: 18, color: context.primaryTextColor)),
                  const SizedBox(height: 8),
                  Text('${s.totalPrice}: ${totalPrice.toStringAsFixed(2)} ${s.sar}',
                      style: TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold, color: context.primaryTextColor)),
                ],
              ),

            const SizedBox(height: 30),

            // زر تأكيد الحجز
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.yellow[700],
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: (datesValid && agreedToPolicies && !isCreatingReservation)
                  ? () => _createReservationAndProceed(context, totalPrice)
                  : null,
              child: isCreatingReservation
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                      ),
                    )
                  : Text(
                      s.confirmReservation,
                      style: const TextStyle(color: Colors.black),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // 🔵 Widgets مساعدة لترتيب الكود:
  Widget buildDateTile(String label, DateTime? date, bool isCheckIn) {
    return ListTile(
      leading: const Icon(Icons.calendar_today),
      title: Text(
        date == null
            ? '${S.of(context).select} $label ${S.of(context).date}'
            : '$label: ${formatter.format(date)}',
      ),
      onTap: () => selectDate(isCheckIn: isCheckIn),
    );
  }

  Widget buildCounterRow(String label, int value, Function(int) onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: TextStyle(fontSize: 18, color: context.primaryTextColor)),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.remove_circle_outline),
              onPressed: value > (label == S.of(context).adults ? 1 : 0)
                  ? () => onChanged(value - 1)
                  : null,
            ),
            Text('$value', style: TextStyle(fontSize: 18, color: context.primaryTextColor)),
            IconButton(
              icon: const Icon(Icons.add_circle_outline),
              onPressed: () => onChanged(value + 1),
            ),
          ],
        ),
      ],
    );
  }



  Future<void> selectDate({required bool isCheckIn}) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isCheckIn) {
          checkInDate = picked;
          if (checkOutDate != null && checkOutDate!.isBefore(checkInDate!)) {
            checkOutDate = null;
          }
        } else {
          checkOutDate = picked;
        }
      });
    }
  }

  Future<void> _createReservationAndProceed(
      BuildContext context, double totalPrice) async {
    if (isCreatingReservation) return; // Prevent duplicate calls

    setState(() {
      isCreatingReservation = true;
    });

    try {
      print('🔄 Starting reservation creation...');

      // Create reservation through API
      final reservationsService = ReservationsApiService(getIt<DioConsumer>());
      print('✅ ReservationsApiService created');

      // Add time component to dates (14:00 for check-in, 12:00 for check-out)
      final checkInDateTime = DateTime(
        checkInDate!.year,
        checkInDate!.month,
        checkInDate!.day,
        14, // 2 PM check-in
        0,
        0,
      );

      final checkOutDateTime = DateTime(
        checkOutDate!.year,
        checkOutDate!.month,
        checkOutDate!.day,
        12, // 12 PM check-out
        0,
        0,
      );

      // Use English locale for date formatting to avoid Arabic numerals
      final dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss', 'en_US');

      final fromDate = dateFormatter.format(checkInDateTime);
      final toDate = dateFormatter.format(checkOutDateTime);

      print('📅 Formatted dates:');
      print('   From: $fromDate');
      print('   To: $toDate');
      print('🏠 Property ID: ${widget.serviceCategoryItemId}');

      final reservationResult = await reservationsService.createReservation(
        serviceCategoryItemId: widget.serviceCategoryItemId,
        reservationFrom: fromDate,
        reservationTo: toDate,
        additionalData: {
          'adults': adults,
          'children': children,
        },
      );

      print(
          '✅ Reservation created successfully with ID: ${reservationResult.id}');

      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // No dialog to close - loading state is handled by button

      // Navigate to booking confirmation screen
      if (context.mounted) {
        print('🔄 Navigating to booking confirmation screen...');
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BookingConfirmationScreen(
              reservationId: reservationResult.id,
              placeTitle: widget.placeTitle,
              checkIn: checkInDate!,
              checkOut: checkOutDate!,
              adults: adults,
              children: children,
              totalPrice: totalPrice,
            ),
          ),
        );
        print('✅ Navigation completed');
      }
    } catch (e) {
      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // Show error message
      if (context.mounted) {
        _showErrorMessage(context, _getErrorMessage(e.toString()));
      }
    }
  }

  /// Get user-friendly error message from API error
  String _getErrorMessage(String error) {
    final s = S.of(context);

    // Check for common error patterns and return user-friendly messages
    if (error.contains('network') || error.contains('connection')) {
      return s.networkError;
    } else if (error.contains('timeout')) {
      return s.timeoutError;
    } else if (error.contains('unauthorized') || error.contains('401')) {
      return s.unauthorizedError;
    } else if (error.contains('forbidden') || error.contains('403')) {
      return s.forbiddenError;
    } else if (error.contains('not found') || error.contains('404')) {
      return s.notFoundError;
    } else if (error.contains('conflict') || error.contains('409')) {
      return s.conflictError;
    } else {
      return s.genericError;
    }
  }

  /// Show error message using ScaffoldMessenger
  void _showErrorMessage(BuildContext context, String message) {
    final s = S.of(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: s.ok,
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Build policies section showing booking rules and cancellation policy
  Widget _buildPoliciesSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Booking Rules Section
        if (widget.bookingRules != null && widget.bookingRules!.isNotEmpty) ...[
          Text(
            s.bookingRules,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.infoColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: context.infoColor.withValues(alpha: 0.3)),
            ),
            child: Text(
              widget.bookingRules!,
              style: TextStyle(
                fontSize: 14,
                color: context.secondaryTextColor,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Cancellation Policy Section
        if (_hasCancellationPolicy()) ...[
          Text(
            s.cancellationPolicy,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildCancellationPolicyContent(s),
        ],
      ],
    );
  }

  /// Check if there's any cancellation policy to display
  bool _hasCancellationPolicy() {
    return (widget.cancellationPolicy != null) ||
           (widget.cancelationRules != null && widget.cancelationRules!.isNotEmpty);
  }

  /// Build cancellation policy content
  Widget _buildCancellationPolicyContent(S s) {
    if (widget.cancellationPolicy != null) {
      final policy = widget.cancellationPolicy!;
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: context.warningColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: context.warningColor.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Policy name/type
            Text(
              policy.name.isNotEmpty ? policy.name : policy.policyTypeDisplayName,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: context.warningColor,
              ),
            ),
            const SizedBox(height: 8),

            // Policy description
            Text(
              policy.description.isNotEmpty ? policy.description : policy.formattedDescription,
              style: TextStyle(
                fontSize: 14,
                color: context.secondaryTextColor,
                height: 1.4,
              ),
            ),

            // Policy details
            if (policy.refundPercentage > 0) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.percent, size: 16, color: context.warningColor),
                  const SizedBox(width: 4),
                  Text(
                    '${s.refundPercentage}: ${policy.refundPercentage.toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 12,
                      color: context.captionTextColor,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    } else if (widget.cancelationRules != null && widget.cancelationRules!.isNotEmpty) {
      // Legacy cancellation rules
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: context.warningColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: context.warningColor.withValues(alpha: 0.3)),
        ),
        child: Text(
          widget.cancelationRules!,
          style: TextStyle(
            fontSize: 14,
            color: context.secondaryTextColor,
            height: 1.4,
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
