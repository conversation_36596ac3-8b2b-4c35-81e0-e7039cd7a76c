import 'package:flutter/material.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/home/<USER>/views/booking_confirmation_screen.dart';
import 'package:intl/intl.dart';

class ReservationCheckConfirmationScreen extends StatefulWidget {
  final int serviceCategoryItemId;
  final String placeTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final ReservationCheckResult checkResult;
  final String reservationFrom;
  final String reservationTo;

  const ReservationCheckConfirmationScreen({
    super.key,
    required this.serviceCategoryItemId,
    required this.placeTitle,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.checkResult,
    required this.reservationFrom,
    required this.reservationTo,
  });

  @override
  State<ReservationCheckConfirmationScreen> createState() =>
      _ReservationCheckConfirmationScreenState();
}

class _ReservationCheckConfirmationScreenState
    extends State<ReservationCheckConfirmationScreen> {
  bool isCreatingReservation = false;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final formatter = NumberFormat("#,##0.00", "en_US");

    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(
          s.confirmReservation,
          style: TextStyle(color: context.primaryTextColor),
        ),
        backgroundColor: context.backgroundColor,
        foregroundColor: context.primaryTextColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBookingSummary(context, s),
              const SizedBox(height: 20),
              _buildPricingDetails(context, s, formatter),
              const SizedBox(height: 20),
              _buildPolicyInfo(context, s),
              const SizedBox(height: 30),
              _buildConfirmButton(context, s),
              const SizedBox(height: 100), // Extra padding for bottom navigation
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingSummary(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.bookingSummary,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildSummaryRow(s.property, widget.placeTitle, context),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.checkIn,
            DateFormat('dd/MM/yyyy').format(widget.checkIn),
            context,
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.checkOut,
            DateFormat('dd/MM/yyyy').format(widget.checkOut),
            context,
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.guests,
            '${s.adults}: ${widget.adults}, ${s.children}: ${widget.children}',
            context,
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            s.numberOfNights,
            '${widget.checkResult.totalDays}',
            context,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: context.secondaryTextColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: context.primaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPricingDetails(BuildContext context, S s, NumberFormat formatter) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.priceBreakdown,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildPriceRow(
            '${s.basePrice} (${widget.checkResult.totalDays} ${s.nights})',
            '${formatter.format(widget.checkResult.price)} ${s.sar}',
            context,
          ),
          if (widget.checkResult.commission > 0) ...[
            const SizedBox(height: 8),
            _buildPriceRow(
              s.serviceFee,
              '${formatter.format(widget.checkResult.commission)} ${s.sar}',
              context,
            ),
          ],
          const Divider(height: 24),
          _buildPriceRow(
            s.totalPrice,
            '${formatter.format(widget.checkResult.finalPrice)} ${s.sar}',
            context,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, BuildContext context, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: context.primaryTextColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color: isTotal ? context.accentColor : context.primaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPolicyInfo(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.infoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: context.infoColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: context.infoColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              s.reservationConfirmationNote,
              style: TextStyle(
                fontSize: 14,
                color: context.secondaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context, S s) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isCreatingReservation ? null : () => _createReservation(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: context.accentColor,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: isCreatingReservation
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Text(
                s.confirmAndProceed,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<void> _createReservation(BuildContext context) async {
    if (isCreatingReservation) return;

    setState(() {
      isCreatingReservation = true;
    });

    try {
      final reservationsService = ReservationsApiService(getIt<DioConsumer>());

      final reservationResult = await reservationsService.createReservation(
        serviceCategoryItemId: widget.serviceCategoryItemId,
        reservationFrom: widget.reservationFrom,
        reservationTo: widget.reservationTo,
        additionalData: {
          'adults': widget.adults,
          'children': widget.children,
        },
      );

      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // Navigate to payment confirmation screen
      if (context.mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => BookingConfirmationScreen(
              reservationId: reservationResult.id,
              placeTitle: widget.placeTitle,
              checkIn: widget.checkIn,
              checkOut: widget.checkOut,
              adults: widget.adults,
              children: widget.children,
              totalPrice: widget.checkResult.finalPrice,
            ),
          ),
        );
      }
    } catch (e) {
      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // Show error message
      if (context.mounted) {
        _showErrorMessage(context, _getErrorMessage(e.toString()));
      }
    }
  }

  String _getErrorMessage(String error) {
    final s = S.of(context);
    
    if (error.contains('network') || error.contains('connection')) {
      return s.networkError;
    } else if (error.contains('timeout')) {
      return s.timeoutError;
    } else if (error.contains('unauthorized') || error.contains('401')) {
      return s.unauthorizedError;
    } else if (error.contains('forbidden') || error.contains('403')) {
      return s.forbiddenError;
    } else if (error.contains('not found') || error.contains('404')) {
      return s.notFoundError;
    } else if (error.contains('conflict') || error.contains('409')) {
      return s.conflictError;
    } else {
      return s.genericError;
    }
  }

  void _showErrorMessage(BuildContext context, String message) {
    final s = S.of(context);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: s.ok,
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
