// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class SAr extends S {
  SAr([String locale = 'ar']) : super(locale);

  @override
  String get preferredByGuests => 'مفضّل لدى الضيوف';

  @override
  String ratingDescription(Object percent) {
    return 'هذا المسكن من أفضل $percent% من المساكن المماثلة، استناداً إلى التقييمات والمراجعات وبيانات الموثوقية';
  }

  @override
  String viewAllReviewsWithCount(Object count) {
    return 'عرض جميع التقييمات البالغ عددها $count تقييمًا';
  }

  @override
  String get noReviewsYet => 'لا توجد تقييمات بعد';

  @override
  String get beFirstToReviewThisPlace => 'كن أول من يقيم هذا المكان';

  @override
  String get aboutThisPlace => 'عن هذا المكان';

  @override
  String get showMore => 'عرض المزيد';

  @override
  String get showLess => 'عرض أقل';

  @override
  String get whatThisPlaceOffers => 'ماذا يقدم هذا المكان';

  @override
  String get showAllAmenities => 'عرض جميع المرافق';

  @override
  String get whereYoullBe => 'أين ستكون';

  @override
  String get cancellationPolicy => 'سياسة الإلغاء';

  @override
  String get cancellationRules => 'قواعد الإلغاء';

  @override
  String get superhost => 'مضيف مميز';

  @override
  String get since => 'منذ';

  @override
  String get change => 'تغيير';

  @override
  String get night => 'ليلة';

  @override
  String get checkIn => 'تسجيل الوصول';

  @override
  String get checkOut => 'تسجيل المغادرة';

  @override
  String get cancel => 'إلغاء';

  @override
  String get done => 'تم';

  @override
  String get selectDates => 'اختر التواريخ';

  @override
  String get settings => 'الإعدادات';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get soundClick => 'صوت النقرات';

  @override
  String get soundScroll => 'صوت التمرير';

  @override
  String get language => 'اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get skip => 'تخطي';

  @override
  String get next => 'التالي';

  @override
  String get start => 'ابدأ';

  @override
  String get searchHint => 'ابحث عن وجهتك المفضلة...';

  @override
  String get filter => 'تصفية';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get noResults => 'لا توجد نتائج';

  @override
  String get perNight => 'ر.س / الليلة';

  @override
  String get wifi => 'واي فاي';

  @override
  String get addToFavorites => 'أضف إلى المفضلة';

  @override
  String get appName => 'نقطة تجمع';

  @override
  String get selectCity => 'اختر المدينة';

  @override
  String get detectingLocation => 'جارٍ تحديد المدينة...';

  @override
  String get locationPermissionError =>
      'يرجى تفعيل إذن الموقع لاستخدام التطبيق';

  @override
  String get browseReels => 'تصفح الريلز';

  @override
  String get discoverLatestVisualContent => 'اكتشف أحدث المحتوى المرئي';

  @override
  String get exploreCategories => 'تصفح الأقسام';

  @override
  String get nearbyPlaces => 'الأماكن القريبة';

  @override
  String get popularDestinations => 'الوجهات الشائعة';

  @override
  String get featuredPlaces => 'أماكن مميزة';

  @override
  String get discoverMore => 'اكتشف المزيد';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get bookNow => 'احجز الآن';

  @override
  String get pricePerNight => 'السعر/ليلة';

  @override
  String get guests => 'الضيوف';

  @override
  String get rooms => 'غرف';

  @override
  String get bathrooms => 'الحمامات';

  @override
  String get amenities => 'المرافق';

  @override
  String get location => 'الموقع';

  @override
  String get reviews => 'التقييمات';

  @override
  String get rating => 'التقييم';

  @override
  String get excellent => 'ممتاز';

  @override
  String get veryGood => 'جيد جداً';

  @override
  String get good => 'جيد';

  @override
  String get fair => 'مقبول';

  @override
  String get poor => 'ضعيف';

  @override
  String get soundSettings => 'إعدادات الصوت';

  @override
  String get customizeExperience => 'خصص تجربتك في التطبيق';

  @override
  String get themeEnabled => 'تم تفعيل الوضع المظلم';

  @override
  String get themeDisabled => 'تم تفعيل الوضع الفاتح';

  @override
  String get soundClickEnabled => 'تم تفعيل أصوات النقر';

  @override
  String get soundClickDisabled => 'تم إيقاف أصوات النقر';

  @override
  String get soundScrollEnabled => 'تم تفعيل أصوات التمرير';

  @override
  String get soundScrollDisabled => 'تم إيقاف أصوات التمرير';

  @override
  String get createProperty => 'إنشاء عقار';

  @override
  String get propertyTitle => 'عنوان العقار';

  @override
  String get propertyDescription => 'الوصف';

  @override
  String get selectCategory => 'اختر الفئة';

  @override
  String get titleAndDescription => 'العنوان والوصف';

  @override
  String get pickLocation => 'اختر الموقع';

  @override
  String get imageGallery => 'معرض الصور';

  @override
  String get addImage => 'إضافة صورة';

  @override
  String get availableServices => 'الخدمات المتوفرة';

  @override
  String get selectServices => 'اختر الخدمات المتوفرة';

  @override
  String get pricing => 'التسعير';

  @override
  String get dailyPrice => 'السعر اليومي';

  @override
  String get weeklyPrice => 'السعر الأسبوعي';

  @override
  String get monthlyPrice => 'السعر الشهري';

  @override
  String get commission => 'عمولة';

  @override
  String get bookingDetails => 'تفاصيل وسياسات الحجز';

  @override
  String get numberOfBathrooms => 'عدد الحمامات';

  @override
  String get numberOfBedrooms => 'عدد غرف النوم';

  @override
  String get numberOfGuests => 'عدد الضيوف';

  @override
  String get bookingPolicy => 'سياسة الحجز';

  @override
  String get back => 'رجوع';

  @override
  String get submit => 'إرسال';

  @override
  String get confirmSubmission => 'تأكيد الإرسال';

  @override
  String get city => 'City';

  @override
  String get country => 'Country';

  @override
  String distanceInKm(Object distance) {
    return '$distance km';
  }

  @override
  String get price => 'السعر';

  @override
  String get favorite => 'Favorite';

  @override
  String get removeFromFavorites => 'إزالة من المفضلة';

  @override
  String get noImage => 'No image available';

  @override
  String get confirmSubmissionMessage => 'هل أنت متأكد من إرسال هذا العقار؟';

  @override
  String get error => 'خطأ';

  @override
  String get ok => 'موافق';

  @override
  String get propertyCreatedSuccessfully => 'تم إنشاء العقار بنجاح';

  @override
  String get tapToUploadImages => 'اضغط لرفع الصور';

  @override
  String get latitude => 'خط العرض';

  @override
  String get longitude => 'خط الطول';

  @override
  String get enterPrice => 'أدخل السعر';

  @override
  String get failedToLoadCategories => 'فشل في تحميل الفئات';

  @override
  String get failedToLoadFacilities => 'فشل في تحميل المرافق';

  @override
  String get failedToCreateItem => 'فشل إنشاء العنصر';

  @override
  String get home => 'الرئيسية';

  @override
  String get search => 'البحث';

  @override
  String get reels => 'الريلز';

  @override
  String get profile => 'حسابي';

  @override
  String get myBookings => 'حجوزاتي';

  @override
  String get myListings => 'عقاراتي';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get personalInfo => 'المعلومات الشخصية';

  @override
  String get contactInfo => 'معلومات التواصل';

  @override
  String get additionalInfo => 'معلومات إضافية';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get bio => 'الوصف';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get phone => 'الهاتف';

  @override
  String get gender => 'الجنس';

  @override
  String get birthdate => 'تاريخ الميلاد';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثي';

  @override
  String get notSpecified => 'غير محدد';

  @override
  String get selectBirthdate => 'اختر تاريخ الميلاد';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get profileImage => 'الصورة الشخصية';

  @override
  String get tapToChangeImage => 'اضغط لتغيير الصورة';

  @override
  String get totalBookings => 'إجمالي الحجوزات';

  @override
  String get confirmedBookings => 'الحجوزات المؤكدة';

  @override
  String get totalProperties => 'إجمالي العقارات';

  @override
  String get totalViews => 'إجمالي المشاهدات';

  @override
  String get totalReservations => 'إجمالي الحجوزات';

  @override
  String get all => 'الكل';

  @override
  String get confirmed => 'مؤكدة';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get completed => 'مكتملة';

  @override
  String get cancelled => 'ملغية';

  @override
  String get active => 'نشطة';

  @override
  String get inactive => 'غير نشطة';

  @override
  String get underReview => 'قيد المراجعة';

  @override
  String get totalPrice => 'السعر الإجمالي';

  @override
  String get viewDetails => 'عرض التفاصيل';

  @override
  String get cancelBooking => 'إلغاء الحجز';

  @override
  String get rebookProperty => 'إعادة الحجز';

  @override
  String get editProperty => 'تعديل العقار';

  @override
  String get bedrooms => 'غرف النوم';

  @override
  String get propertyDetails => 'تفاصيل العقار';

  @override
  String get hostMode => 'وضع المستضيف';

  @override
  String get enableHostMode => 'قم بتفعيل وضع المستضيف لإدارة عقاراتك';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get appearance => 'المظهر';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get privacy => 'الخصوصية والأمان';

  @override
  String get about => 'حول التطبيق';

  @override
  String get manageNotifications => 'إدارة إعدادات الإشعارات';

  @override
  String get privacySettings => 'إعدادات الخصوصية والحماية';

  @override
  String get appInfo => 'معلومات التطبيق والإصدار';

  @override
  String get version => 'الإصدار';

  @override
  String get appDescription => 'تطبيق لحجز العقارات والأماكن';

  @override
  String get noBookingsYet => 'لا توجد حجوزات';

  @override
  String get noBookingsSubtitle => 'لم تقم بأي حجوزات حتى الآن';

  @override
  String get exploreProperties => 'استكشف العقارات';

  @override
  String get noPropertiesYet => 'لا توجد عقارات';

  @override
  String get noPropertiesSubtitle => 'ابدأ بإضافة عقارك الأول';

  @override
  String get addProperty => 'إضافة عقار';

  @override
  String get searching => 'جاري البحث...';

  @override
  String get tryDifferentKeywords => 'جرب البحث بكلمات مختلفة';

  @override
  String get backToSearch => 'العودة للبحث';

  @override
  String get loadingReels => 'جاري تحميل الريلز...';

  @override
  String get failedToLoadReels => 'فشل في تحميل الريلز';

  @override
  String get checkConnection => 'تحقق من اتصالك بالإنترنت';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get shareProperty => 'مشاركة العقار';

  @override
  String get yearsHosting => 'years hosting';

  @override
  String get noAmenitiesListed => 'لا توجد مرافق مدرجة';

  @override
  String get mapView => 'عرض الخريطة';

  @override
  String get guestReview => 'تقييم الضيف';

  @override
  String get reserve => 'احجز';

  @override
  String get bookingFee => 'رسوم الحجز';

  @override
  String get serviceFee => 'رسوم الخدمة';

  @override
  String get taxes => 'الضرائب';

  @override
  String get total => 'الإجمالي';

  @override
  String get currentLocation => 'الموقع الحالي';

  @override
  String get searchPlaceholder => 'حياك ... دور علي اللي تبيه';

  @override
  String get categories => 'الفئات';

  @override
  String get popularPlaces => 'الأماكن الشائعة';

  @override
  String get seeAll => 'عرض الكل';

  @override
  String get views => 'المشاهدات';

  @override
  String get properties => 'العقارات';

  @override
  String get reservations => 'الحجوزات';

  @override
  String get status => 'الحالة';

  @override
  String get checkInDate => 'تاريخ تسجيل الوصول';

  @override
  String get checkOutDate => 'تاريخ تسجيل المغادرة';

  @override
  String get propertyName => 'اسم العقار';

  @override
  String get propertyLocation => 'موقع العقار';

  @override
  String get totalAmount => 'المبلغ الإجمالي';

  @override
  String get bookingStatus => 'حالة الحجز';

  @override
  String get bookingDate => 'تاريخ الحجز';

  @override
  String get hostName => 'اسم المضيف';

  @override
  String get contactHost => 'تواصل مع المضيف';

  @override
  String get cancelReservation => 'إلغاء الحجز';

  @override
  String get modifyReservation => 'تعديل الحجز';

  @override
  String get leaveReview => 'اترك تقييم';

  @override
  String get downloadReceipt => 'تحميل الإيصال';

  @override
  String get propertyType => 'نوع العقار';

  @override
  String get description => 'الوصف';

  @override
  String get rules => 'القواعد';

  @override
  String get safetyFeatures => 'ميزات الأمان';

  @override
  String get accessibility => 'إمكانية الوصول';

  @override
  String get nearbyAttractions => 'المعالم القريبة';

  @override
  String get transportation => 'المواصلات';

  @override
  String get checkInInstructions => 'تعليمات تسجيل الوصول';

  @override
  String get houseRules => 'قواعد المنزل';

  @override
  String get importantInfo => 'معلومات مهمة';

  @override
  String get instantBook => 'حجز فوري';

  @override
  String get verified => 'موثق';

  @override
  String get newListing => 'إعلان جديد';

  @override
  String get rareFind => 'اكتشاف نادر';

  @override
  String get guestFavorite => 'مفضل الضيوف';

  @override
  String get topRated => 'الأعلى تقييماً';

  @override
  String get luxuryStay => 'إقامة فاخرة';

  @override
  String get budgetFriendly => 'صديق للميزانية';

  @override
  String get familyFriendly => 'مناسب للعائلات';

  @override
  String get petFriendly => 'مناسب للحيوانات الأليفة';

  @override
  String get workFriendly => 'مناسب للعمل';

  @override
  String get partyFriendly => 'مناسب للحفلات';

  @override
  String get smokingAllowed => 'التدخين مسموح';

  @override
  String get noSmoking => 'ممنوع التدخين';

  @override
  String get freeWifi => 'واي فاي مجاني';

  @override
  String get freeParking => 'موقف مجاني';

  @override
  String get pool => 'مسبح';

  @override
  String get gym => 'صالة رياضية';

  @override
  String get spa => 'سبا';

  @override
  String get restaurant => 'مطعم';

  @override
  String get bar => 'بار';

  @override
  String get laundry => 'غسيل';

  @override
  String get kitchen => 'مطبخ';

  @override
  String get airConditioning => 'تكييف';

  @override
  String get heating => 'تدفئة';

  @override
  String get tv => 'تلفزيون';

  @override
  String get workspace => 'مساحة عمل';

  @override
  String get balcony => 'شرفة';

  @override
  String get garden => 'حديقة';

  @override
  String get beachAccess => 'وصول للشاطئ';

  @override
  String get mountainView => 'إطلالة جبلية';

  @override
  String get cityView => 'إطلالة المدينة';

  @override
  String get oceanView => 'إطلالة المحيط';

  @override
  String get lakeView => 'إطلالة البحيرة';

  @override
  String get gardenView => 'إطلالة الحديقة';

  @override
  String get streetView => 'إطلالة الشارع';

  @override
  String get noView => 'بدون إطلالة';

  @override
  String get bookingSummary => 'ملخص الحجز';

  @override
  String get hostDashboard => 'لوحة المضيف';

  @override
  String get walletBalance => 'رصيد المحفظة';

  @override
  String get totalEarnings => 'إجمالي الأرباح';

  @override
  String get recentBookings => 'أحدث الحجوزات';

  @override
  String get recentReviews => 'أحدث التعليقات';

  @override
  String get withdraw => 'سحب';

  @override
  String get earnings => 'الأرباح';

  @override
  String get bookingsChart => 'مخطط الحجوزات';

  @override
  String get earningsChart => 'مخطط الأرباح';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get lastMonth => 'الشهر الماضي';

  @override
  String get thisYear => 'هذا العام';

  @override
  String get availableBalance => 'الرصيد المتاح';

  @override
  String get pendingEarnings => 'الأرباح المعلقة';

  @override
  String get totalWithdrawn => 'إجمالي المسحوب';

  @override
  String get withdrawFunds => 'سحب الأموال';

  @override
  String get enterAmount => 'أدخل المبلغ';

  @override
  String get minimumWithdraw => 'الحد الأدنى للسحب: ر.س 50';

  @override
  String get withdrawalMethod => 'طريقة السحب';

  @override
  String get bankTransfer => 'تحويل بنكي';

  @override
  String get paypal => 'باي بال';

  @override
  String get processing => 'قيد المعالجة';

  @override
  String get noRecentBookings => 'لا توجد حجوزات حديثة';

  @override
  String get noRecentReviews => 'لا توجد تعليقات حديثة';

  @override
  String get average => 'متوسط';

  @override
  String get guestName => 'اسم الضيف';

  @override
  String get checkInOut => 'الوصول/المغادرة';

  @override
  String get nightsStayed => 'عدد الليالي';

  @override
  String get earningsOverview => 'نظرة عامة على الأرباح';

  @override
  String get bookingsOverview => 'نظرة عامة على الحجوزات';

  @override
  String get last6Months => 'آخر 6 أشهر';

  @override
  String get guestComment => 'تعليق الضيف';

  @override
  String get dates => 'التواريخ';

  @override
  String get nights => 'ليالي';

  @override
  String get noData => 'لا توجد بيانات';

  @override
  String get enterSearchTerm => 'أدخل كلمة البحث...';

  @override
  String get noSearchResults => 'لا توجد نتائج';

  @override
  String get noTitle => 'بدون عنوان';

  @override
  String get noDescription => 'بدون وصف';

  @override
  String get tryDifferentSearch => 'جرب البحث بكلمات مختلفة';

  @override
  String get searchResults => 'نتائج البحث';

  @override
  String get noBookingsMessage => 'لا توجد حجوزات حديثة';

  @override
  String get noReviewsMessage => 'لا توجد تعليقات حديثة';

  @override
  String get hostModeDescription => 'قم بتفعيل وضع المستضيف لإدارة عقاراتك';

  @override
  String get logoutConfirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get mustLogin => 'يجب عليك تسجيل الدخول';

  @override
  String get mustLoginDescription => 'قم بتسجيل الدخول لعرض حسابك الشخصي';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get confirmReservation => 'تأكيد الحجز';

  @override
  String get unitDetails => 'تفاصيل الوحدة';

  @override
  String get unitName => 'اسم الوحدة';

  @override
  String get numberOfDays => 'عدد الأيام';

  @override
  String get reservationFrom => 'تاريخ الحجز من';

  @override
  String get reservationTo => 'تاريخ الحجز إلى';

  @override
  String get priceDetails => 'تفاصيل السعر';

  @override
  String get finalPrice => 'السعر النهائي';

  @override
  String get priceBredown => 'تفاصيل الأسعار';

  @override
  String get priceType => 'نوع السعر';

  @override
  String get weekendPrice => 'سعر عطلة نهاية الأسبوع';

  @override
  String get normalDays => 'أيام العمل العادية';

  @override
  String get weekendDays => 'أيام العطلة الأسبوعية';

  @override
  String get confirmBooking => 'تأكيد الحجز';

  @override
  String get reservationConfirmed => 'تم تأكيد الحجز بنجاح!';

  @override
  String get reservationFailed => 'فشل تأكيد الحجز';

  @override
  String get invalidDate => 'تاريخ غير صالح';

  @override
  String get notAvailable => 'غير متوفر';

  @override
  String get days => 'أيام';

  @override
  String get reviewsOverview => 'نظرة عامة على التقييمات';

  @override
  String get totalReviews => 'إجمالي التقييمات';

  @override
  String get allReviews => 'الكل';

  @override
  String get highRated => 'عالي التقييم';

  @override
  String get noReviewsFound => 'لا توجد تقييمات';

  @override
  String get noReviewsMatchFilter => 'لا توجد تقييمات تطابق الفلتر المحدد';

  @override
  String get foundHelpful => 'شخص وجد هذا التقييم مفيداً';

  @override
  String get selectReservationDate => 'اختيار تاريخ الحجز';

  @override
  String get reviewReservation => 'مراجعة الحجز';

  @override
  String get pleaseSelectBothDates => 'يرجى اختيار كلا التاريخين';

  @override
  String get selectedPeriodNotAvailable =>
      'الفترة المحددة غير متاحة، يرجى اختيار فترة أخرى.';

  @override
  String get errorFetchingInfo => 'حدث خطأ أثناء جلب المعلومات';

  @override
  String get failedToLoadVideo => 'فشل في تحميل الفيديو';

  @override
  String get gatherPoint => 'نقطة تجمع';

  @override
  String get muteVideo => 'كتم الصوت';

  @override
  String get unmuteVideo => 'إلغاء كتم الصوت';

  @override
  String get playVideo => 'تشغيل الفيديو';

  @override
  String get pauseVideo => 'إيقاف الفيديو';

  @override
  String get comment => 'تعليق';

  @override
  String get share => 'مشاركة';

  @override
  String get comments => 'التعليقات';

  @override
  String get writeComment => 'اكتب تعليق...';

  @override
  String get postComment => 'نشر التعليق';

  @override
  String get noComments => 'لا توجد تعليقات';

  @override
  String get commentPosted => 'تم نشر التعليق بنجاح';

  @override
  String get commentFailed => 'فشل في نشر التعليق';

  @override
  String get deleteComment => 'حذف التعليق';

  @override
  String get editComment => 'تعديل التعليق';

  @override
  String get replyToComment => 'رد على التعليق';

  @override
  String get showComments => 'عرض التعليقات';

  @override
  String get hideComments => 'إخفاء التعليقات';

  @override
  String get searchReels => 'البحث في الريلز';

  @override
  String get filterReels => 'تصفية الريلز';

  @override
  String get allCategories => 'جميع الفئات';

  @override
  String get sortBy => 'ترتيب حسب';

  @override
  String get newest => 'الأحدث';

  @override
  String get oldest => 'الأقدم';

  @override
  String get mostLiked => 'الأكثر إعجاباً';

  @override
  String get mostCommented => 'الأكثر تعليقاً';

  @override
  String get applyFilter => 'تطبيق التصفية';

  @override
  String get clearFilter => 'مسح التصفية';

  @override
  String get noResultsFound => 'لا توجد نتائج';

  @override
  String get additionalSettings => 'إعدادات إضافية';

  @override
  String get notificationSettings => 'إعدادات الإشعارات';

  @override
  String get aboutApp => 'حول التطبيق';

  @override
  String get filterResults => 'تصفية النتائج';

  @override
  String get priceRange => 'نطاق السعر';

  @override
  String get minimumRating => 'الحد الأدنى للتقييم';

  @override
  String get resetFilters => 'إعادة تعيين';

  @override
  String get applyFilters => 'تطبيق';

  @override
  String get tryDifferentSearchCriteria => 'جرب تعديل معايير البحث';

  @override
  String get priceLowToHigh => 'السعر: من الأقل للأعلى';

  @override
  String get priceHighToLow => 'السعر: من الأعلى للأقل';

  @override
  String get ratingHighToLow => 'التقييم: من الأعلى للأقل';

  @override
  String get ratingLowToHigh => 'التقييم: الأقل أولاً';

  @override
  String get popular => 'الأكثر شهرة';

  @override
  String get privacyAndSecurity => 'إعدادات الخصوصية والأمان';

  @override
  String get appInformation => 'معلومات التطبيق والإصدار';

  @override
  String get pushNotifications => 'الإشعارات الفورية';

  @override
  String get eventNotifications => 'إشعارات الأحداث';

  @override
  String get messageNotifications => 'إشعارات الرسائل';

  @override
  String get marketingNotifications => 'الإشعارات التسويقية';

  @override
  String get enableAllNotifications => 'تفعيل أو إلغاء جميع الإشعارات';

  @override
  String get newEventsAndUpdates => 'إشعارات حول الأحداث الجديدة والتحديثات';

  @override
  String get newMessagesAndChats => 'إشعارات الرسائل الجديدة والمحادثات';

  @override
  String get offersAndMarketing => 'إشعارات العروض والأخبار التسويقية';

  @override
  String get testNotification => 'إشعار تجريبي';

  @override
  String get sendTestNotification => 'إرسال إشعار تجريبي';

  @override
  String get notificationPermissionRequired => 'مطلوب إذن الإشعارات';

  @override
  String get enableNotificationsInSettings =>
      'يرجى تفعيل الإشعارات في إعدادات الجهاز';

  @override
  String get openSettings => 'فتح الإعدادات';

  @override
  String get dataAndPrivacy => 'البيانات والخصوصية';

  @override
  String get dataCollection => 'جمع البيانات';

  @override
  String get thirdPartySharing => 'المشاركة مع الأطراف الثالثة';

  @override
  String get dataRetention => 'الاحتفاظ بالبيانات';

  @override
  String get yourRights => 'حقوقك';

  @override
  String get contactUs => 'تواصل معنا';

  @override
  String get deleteAccount => 'حذف الحساب';

  @override
  String get dataCollectionDesc => 'كيف نجمع ونستخدم بياناتك';

  @override
  String get thirdPartySharingDesc => 'معلومات حول مشاركة البيانات مع الشركاء';

  @override
  String get dataRetentionDesc => 'كم من الوقت نحتفظ ببياناتك';

  @override
  String get yourRightsDesc => 'حقوق الخصوصية الخاصة بك وكيفية ممارستها';

  @override
  String get contactUsDesc => 'تواصل مع فريق الدعم لدينا';

  @override
  String get deleteAccountDesc => 'حذف حسابك وبياناتك نهائياً';

  @override
  String get proceedWithGoogle => 'المتابعة مع جوجل';

  @override
  String get proceedWithApple => 'المتابعة مع آبل';

  @override
  String get or => 'أو';

  @override
  String get welcomeGuest => 'حيا الله ضيفنا ... متابعة كضيف';

  @override
  String get proceedAsGuest => 'متابعة كضيف';

  @override
  String get proceedWithPhone => 'تابع بأستخدام رقم جوالك';

  @override
  String get verifyPhoneNumber => 'التحقق من رقم جوالك';

  @override
  String get enterVerificationCode => 'أدخل كود التحقق';

  @override
  String get verificationCodeSent =>
      'لقد تم إرسال رمز مكون من 4 أرقام إلى جوالك';

  @override
  String get proceedLabel => 'استمرار';

  @override
  String get viewAllBookings => 'عرض جميع الحجوزات';

  @override
  String get viewAllReviews => 'عرض جميع التقييمات';

  @override
  String get enterPhoneNumber => 'ادخل رقم الجوال';

  @override
  String get phoneNumberHint => '5xxxxxxxx';

  @override
  String get pleaseEnterPhoneNumber => 'الرجاء ادخال رقم الجوال';

  @override
  String get pleaseCheckPhoneNumber => 'الرجاء التحقق من رقم الجوال';

  @override
  String get invalidPhoneNumber => 'رقم الجوال غير صحيح';

  @override
  String get phoneNumberRequired => 'رقم الهاتف مطلوب';

  @override
  String get resendCode => 'إعادة إرسال الكود';

  @override
  String get didntReceiveCode => 'لم تستلم الكود؟';

  @override
  String get verificationFailed => 'فشل التحقق';

  @override
  String get invalidCode => 'كود غير صحيح';

  @override
  String get codeExpired => 'انتهت صلاحية الكود';

  @override
  String get continueButton => 'المتابعة';

  @override
  String get exploreAllCategoriesSubtitle => 'استكشف جميع الفئات المتاحة';

  @override
  String get basicInformation => 'المعلومات الأساسية';

  @override
  String get enterPropertyTitle => 'أدخل عنوان العقار';

  @override
  String get pleaseEnterPropertyTitle => 'يرجى إدخال عنوان العقار';

  @override
  String get enterPropertyDescription => 'أدخل وصف العقار';

  @override
  String get pleaseEnterDescription => 'يرجى إدخال الوصف';

  @override
  String get pleaseEnterPrice => 'يرجى إدخال السعر';

  @override
  String get pleaseEnterValidPrice => 'يرجى إدخال سعر صحيح';

  @override
  String get maxGuests => 'الحد الأقصى للضيوف';

  @override
  String get pleaseEnterMaxGuests => 'يرجى إدخال الحد الأقصى للضيوف';

  @override
  String get pleaseEnterValidNumber => 'يرجى إدخال رقم صحيح';

  @override
  String get pleaseEnterBedrooms => 'يرجى إدخال عدد غرف النوم';

  @override
  String get pleaseBathrooms => 'يرجى إدخال عدد الحمامات';

  @override
  String get category => 'الفئة';

  @override
  String get pleaseSelectCategory => 'يرجى اختيار الفئة';

  @override
  String get facilities => 'المرافق';

  @override
  String get media => 'الوسائط';

  @override
  String get mainImage => 'الصورة الرئيسية';

  @override
  String get video => 'الفيديو';

  @override
  String get gallery => 'المعرض';

  @override
  String get addImages => 'إضافة صور';

  @override
  String get bookingRules => 'قواعد الحجز';

  @override
  String get enterBookingRules => 'أدخل قواعد الحجز';

  @override
  String get enterCancellationRules => 'أدخل قواعد الإلغاء';

  @override
  String get reviewSubmittedSuccessfully => 'تم إرسال التقييم بنجاح';

  @override
  String get submitReview => 'إرسال التقييم';

  @override
  String get loginRequiredForReservation => 'تسجيل الدخول مطلوب للحجز';

  @override
  String get loginRequiredForFavorites => 'تسجيل الدخول مطلوب للمفضلة';

  @override
  String get loginRequiredForReviews => 'تسجيل الدخول مطلوب للتقييمات';

  @override
  String get guestModeInfo => 'معلومات وضع الضيف';

  @override
  String get guestReservation => 'حجز الضيف';

  @override
  String get guestReservationMessage =>
      'يمكنك الحجز كضيف ولكن ستحتاج لتسجيل الدخول لاحقاً';

  @override
  String get guestLimitations => 'قيود وضع الضيف';

  @override
  String get guestLimitationsDetails => 'في وضع الضيف، بعض الميزات محدودة';

  @override
  String get loginForBetterExperience => 'سجل دخولك للحصول على تجربة أفضل';

  @override
  String get continueAsGuest => 'المتابعة كضيف';

  @override
  String get featureUnavailable => 'الميزة غير متاحة';

  @override
  String get featureRequiresLogin => 'هذه الميزة تتطلب تسجيل الدخول';

  @override
  String get guest => 'ضيف';

  @override
  String get retryConnection => 'إعادة المحاولة';

  @override
  String get connectionError => 'خطأ في الاتصال';

  @override
  String get serverError => 'حدث خطأ في الخادم';

  @override
  String get unknownError => 'خطأ غير معروف';

  @override
  String get loadingData => 'جاري تحميل البيانات';

  @override
  String get refreshData => 'تحديث البيانات';

  @override
  String get noInternetConnection => 'لا يوجد اتصال بالإنترنت';

  @override
  String get checkInternetConnection => 'تحقق من اتصال الإنترنت';

  @override
  String get dataLoadFailed => 'فشل في تحميل البيانات';

  @override
  String get pullToRefresh => 'اسحب للتحديث';

  @override
  String get ofPreposition => 'من';

  @override
  String get tourismPermitNumber => 'رقم تصريح السياحة *';

  @override
  String get dataLoadError => 'حدث خطأ في تحميل البيانات';

  @override
  String get noDataAvailable => 'لا توجد بيانات متاحة';

  @override
  String get reviewsCount => 'تقييم';

  @override
  String get sarPerNight => 'ر.س/ليلة';

  @override
  String get freeWifiArabic => 'واي فاي مجاني';

  @override
  String get hostedBy => 'Hosted by';

  @override
  String get year => 'سنة';

  @override
  String get years => 'سنوات';

  @override
  String get inHosting => 'في الاستضافة';

  @override
  String get newHost => 'مستضيف جديد';

  @override
  String get noDescriptionAvailable => 'لا يوجد وصف متاح.';

  @override
  String get guestRating => 'تقييم الضيف';

  @override
  String get march2024 => 'مارس 2024';

  @override
  String get sampleReviewText =>
      'مكان رائع للإقامة! نظيف ومريح وكما هو موصوف تماماً. المضيف كان متجاوباً ومفيداً جداً.';

  @override
  String get showAllReviews => 'عرض جميع';

  @override
  String get sar => 'ر.س';

  @override
  String get currencySymbol => 'ر.س';

  @override
  String get currencyCode => 'ر.س';

  @override
  String priceWithCurrency(String price) {
    return '$price ر.س';
  }

  @override
  String get minimumWithdrawAmount => 'الحد الأدنى للسحب: ر.س 50';

  @override
  String get smartEntry => 'دخول ذكي';

  @override
  String get knowledge => 'المعارف';

  @override
  String get previousTrips => 'الرحلات السابقة';

  @override
  String get joinAsHost => 'انضم كمضيف';

  @override
  String get joinAsHostSubtitle => 'من السهل بدء الاستضافة وربح إضافي';

  @override
  String get accountSettings => 'إعدادات الحساب';

  @override
  String get requestHelp => 'اطلب المساعدة';

  @override
  String get viewProfile => 'عرض الملف الشخصي';

  @override
  String get referHost => 'إحالة مضيف';

  @override
  String get legal => 'قانوني';

  @override
  String get previousTrip => 'رحلة سابقة';

  @override
  String get yearsOnAirbnb => 'سنة على Airbnb';

  @override
  String get cancellationPolicyNote =>
      'تذكر أن السياسة التي يضعها المضيف تناسب ظروفك، في حالات نادرة قد تكون مؤهلاً لاسترداد جزئي أو كامل وفقاً لسياسة الموقع.';

  @override
  String get selectCancellationPolicy => 'اختر سياسة الإلغاء';

  @override
  String get shortTermBookings => 'الحجوزات قصيرة المدى (≤28 يوم)';

  @override
  String get longTermBookings => 'الحجوزات طويلة المدى (>28 يوم)';

  @override
  String get flexiblePolicy => 'مرنة';

  @override
  String get moderatePolicy => 'معتدلة';

  @override
  String get strictPolicy => 'مشددة';

  @override
  String get refundPercentage => 'نسبة الاسترداد';

  @override
  String get cancellationWindow => 'مهلة الإلغاء';

  @override
  String get bookingWindow => 'مهلة ما بعد الحجز';

  @override
  String get minimumNotice => 'الحد الأدنى للإشعار';

  @override
  String get serviceFeeRefundable => 'رسوم الخدمة قابلة للاسترداد';

  @override
  String get serviceFeeNotRefundable => 'رسوم الخدمة غير قابلة للاسترداد';

  @override
  String get cleaningFeeRefundable => 'رسوم التنظيف قابلة للاسترداد';

  @override
  String get cleaningFeeNotRefundable => 'رسوم التنظيف غير قابلة للاسترداد';

  @override
  String get hours => 'ساعة';

  @override
  String get paymentMethod => 'طريقة الدفع';

  @override
  String get adults => 'البالغين';

  @override
  String get children => 'الأطفال';

  @override
  String get agreeToBookingPolicies => 'أوافق على سياسات الحجز';

  @override
  String get mustAgreeToBookingPolicies =>
      'يجب الموافقة على السياسات لتأكيد الحجز';

  @override
  String get numberOfNights => 'عدد الليالي';

  @override
  String get checkOutMustBeAfterCheckIn =>
      'يجب أن يكون تاريخ المغادرة بعد تاريخ الوصول';

  @override
  String get reservationError => 'خطأ في الحجز';

  @override
  String get networkError => 'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى';

  @override
  String get timeoutError => 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';

  @override
  String get unauthorizedError => 'يرجى تسجيل الدخول والمحاولة مرة أخرى';

  @override
  String get forbiddenError => 'ليس لديك صلاحية لإجراء هذا الحجز';

  @override
  String get notFoundError => 'العقار غير متاح حالياً';

  @override
  String get conflictError =>
      'التواريخ المحددة غير متاحة، يرجى اختيار تواريخ أخرى';

  @override
  String get genericError =>
      'حدث خطأ أثناء إنشاء الحجز، يرجى المحاولة مرة أخرى';

  @override
  String get select => 'اختر';

  @override
  String get date => 'تاريخ';

  @override
  String get property => 'العقار';

  @override
  String get priceBreakdown => 'تفاصيل السعر';

  @override
  String get basePrice => 'السعر الأساسي';

  @override
  String get reservationConfirmationNote =>
      'سيتم إنشاء الحجز بعد تأكيدك. يمكنك المتابعة للدفع بعد ذلك.';

  @override
  String get confirmAndProceed => 'تأكيد والمتابعة';

  @override
  String get policyDescription => 'وصف السياسة';

  @override
  String get pleaseSelectPropertyType => 'يرجى اختيار نوع العقار';

  @override
  String get pleaseSelectCancellationPolicy => 'يرجى اختيار سياسة الإلغاء';

  @override
  String get selectLocation => 'اختر الموقع';

  @override
  String get confirmLocation => 'تأكيد الموقع';

  @override
  String get confirm => 'تأكيد';

  @override
  String get propertyPreview => 'معاينة العقار';

  @override
  String get publishProperty => 'نشر العقار';

  @override
  String get policies => 'السياسات';

  @override
  String get listView => 'عرض القائمة';

  @override
  String get gridView => 'عرض الشبكة';

  @override
  String get cancelSelection => 'إلغاء التحديد';

  @override
  String get selectMultiple => 'تحديد متعدد';

  @override
  String get addNewListing => 'إضافة إعلان جديد';

  @override
  String get dashboardOverview => 'نظرة عامة';

  @override
  String get totalRevenue => 'إجمالي الإيرادات';

  @override
  String get averageRating => 'متوسط التقييم';

  @override
  String get activeListings => 'العقارات النشطة';

  @override
  String get occupancyRate => 'معدل الإشغال';

  @override
  String get conversionRate => 'معدل التحويل';

  @override
  String get allListings => 'جميع العقارات';

  @override
  String get inactiveListings => 'العقارات غير النشطة';

  @override
  String get drafts => 'المسودات';

  @override
  String get pendingReview => 'قيد المراجعة';

  @override
  String get topPerforming => 'الأفضل أداءً';

  @override
  String get needsAttention => 'تحتاج اهتمام';

  @override
  String get searchListings => 'البحث في العقارات...';

  @override
  String get clearFilters => 'مسح الفلاتر';

  @override
  String get noListingsYet => 'لا توجد عقارات بعد';

  @override
  String get noListingsDescription =>
      'ابدأ رحلة الاستضافة بإنشاء أول عقار لك. شارك مساحتك مع المسافرين وابدأ في الكسب!';

  @override
  String get createFirstListing => 'إنشاء أول عقار';

  @override
  String get hostingTips => 'نصائح الاستضافة';

  @override
  String get tip1 => 'أضف صور عالية الجودة لجذب المزيد من الضيوف';

  @override
  String get tip2 => 'اكتب وصفاً مفصلاً لعقارك';

  @override
  String get tip3 => 'حدد أسعاراً تنافسية لمنطقتك';

  @override
  String get needHelp => 'تحتاج مساعدة؟';

  @override
  String get contactSupport => 'تواصل مع الدعم';

  @override
  String get errorLoadingListings => 'خطأ في تحميل العقارات';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get errorPersistsContact => 'إذا استمر الخطأ، يرجى التواصل مع الدعم';

  @override
  String get listingStatus => 'حالة العقار';

  @override
  String get publishListing => 'نشر العقار';

  @override
  String get editListing => 'تعديل العقار';

  @override
  String get deactivate => 'إلغاء تفعيل';

  @override
  String get activate => 'تفعيل';

  @override
  String get editWhilePending => 'تعديل أثناء المراجعة';

  @override
  String get rejectionReason => 'سبب الرفض';

  @override
  String get pendingReservations => 'حجوزات معلقة';

  @override
  String get viewReservations => 'عرض الحجوزات';

  @override
  String get activeStatusDescription => 'عقارك مُفعل ومرئي للضيوف';

  @override
  String get inactiveStatusDescription => 'عقارك مخفي عن الضيوف';

  @override
  String get draftStatusDescription => 'عقارك محفوظ ولكن غير منشور بعد';

  @override
  String get pendingStatusDescription => 'عقارك قيد المراجعة من فريقنا';

  @override
  String get suspendedStatusDescription => 'تم تعليق عقارك';

  @override
  String get unknownStatusDescription => 'حالة غير معروفة';

  @override
  String get publishListingConfirmation => 'هل أنت متأكد من نشر هذا العقار؟';

  @override
  String get deactivateListingConfirmation =>
      'هل أنت متأكد من إلغاء تفعيل هذا العقار؟';

  @override
  String get deactivateListing => 'إلغاء تفعيل العقار';

  @override
  String get changeListingStatus => 'تغيير حالة العقار';

  @override
  String get currentStatus => 'الحالة الحالية';

  @override
  String get selectNewStatus => 'اختر الحالة الجديدة';

  @override
  String get changeReason => 'سبب التغيير';

  @override
  String get enterChangeReason => 'أدخل سبب تغيير الحالة...';

  @override
  String get changeStatus => 'تغيير الحالة';

  @override
  String get statusChangedSuccessfully => 'تم تغيير الحالة بنجاح';

  @override
  String get statusChangeError => 'خطأ في تغيير الحالة';

  @override
  String get bulkActions => 'إجراءات جماعية';

  @override
  String get listingsSelected => 'عقارات محددة';

  @override
  String get currentStatusBreakdown => 'تفصيل الحالة الحالية';

  @override
  String get selectAction => 'اختر الإجراء';

  @override
  String get applyAction => 'تطبيق الإجراء';

  @override
  String get activateAll => 'تفعيل الكل';

  @override
  String get deactivateAll => 'إلغاء تفعيل الكل';

  @override
  String get convertToDraft => 'تحويل إلى مسودة';

  @override
  String get deleteAll => 'حذف الكل';

  @override
  String get activateAllDescription => 'جعل جميع العقارات المحددة مرئية للضيوف';

  @override
  String get deactivateAllDescription =>
      'إخفاء جميع العقارات المحددة عن الضيوف';

  @override
  String get convertToDraftDescription =>
      'تحويل جميع العقارات المحددة إلى حالة مسودة';

  @override
  String get deleteAllDescription => 'حذف جميع العقارات المحددة نهائياً';

  @override
  String get bulkActionCompleted => 'تم تنفيذ الإجراء الجماعي بنجاح';

  @override
  String get bulkActionError => 'خطأ في تنفيذ الإجراء الجماعي';

  @override
  String get analyticsOverview => 'نظرة عامة على التحليلات';

  @override
  String get last7Days => 'آخر 7 أيام';

  @override
  String get last30Days => 'آخر 30 يوم';

  @override
  String get last90Days => 'آخر 90 يوم';

  @override
  String get lastYear => 'السنة الماضية';

  @override
  String get exportData => 'تصدير البيانات';

  @override
  String get performanceGrade => 'تقييم الأداء';

  @override
  String get overview => 'نظرة عامة';

  @override
  String get charts => 'الرسوم البيانية';

  @override
  String get insights => 'الرؤى والأفكار';

  @override
  String get details => 'التفاصيل';

  @override
  String get responseTime => 'وقت الاستجابة';

  @override
  String get responseRate => 'معدل الاستجابة';

  @override
  String get viewsTrend => 'اتجاه المشاهدات';

  @override
  String get dailyViews => 'المشاهدات اليومية';

  @override
  String get dailyBookings => 'الحجوزات اليومية';

  @override
  String get dailyRevenue => 'الإيرادات اليومية';

  @override
  String get bookingMetrics => 'مقاييس الحجز';

  @override
  String get cancelledBookings => 'الحجوزات الملغية';

  @override
  String get cancellationRate => 'معدل الإلغاء';

  @override
  String get revenueMetrics => 'مقاييس الإيرادات';

  @override
  String get netRevenue => 'صافي الإيرادات';

  @override
  String get averageDailyRate => 'متوسط السعر اليومي';

  @override
  String get engagementMetrics => 'مقاييس التفاعل';

  @override
  String get uniqueViews => 'المشاهدات الفريدة';

  @override
  String get favoriteCount => 'المفضلة';

  @override
  String get shareCount => 'المشاركات';

  @override
  String get analytics => 'التحليلات';

  @override
  String get advancedBulkActions => 'إجراءات جماعية متقدمة';

  @override
  String get totalValue => 'القيمة الإجمالية';

  @override
  String get selectActionCategory => 'اختر فئة الإجراء';

  @override
  String get statusActions => 'إجراءات الحالة';

  @override
  String get pricingActions => 'إجراءات التسعير';

  @override
  String get managementActions => 'إجراءات الإدارة';

  @override
  String get executeAction => 'تنفيذ الإجراء';

  @override
  String get actionParameters => 'معاملات الإجراء';

  @override
  String get percentage => 'النسبة المئوية';

  @override
  String get newPrice => 'السعر الجديد';

  @override
  String get discountPercentage => 'نسبة الخصم';

  @override
  String get discountDuration => 'مدة الخصم';

  @override
  String get publishAll => 'نشر الكل';

  @override
  String get increasePrices => 'زيادة الأسعار';

  @override
  String get decreasePrices => 'تقليل الأسعار';

  @override
  String get setPrices => 'تحديد الأسعار';

  @override
  String get applyDiscount => 'تطبيق خصم';

  @override
  String get duplicateAll => 'نسخ الكل';

  @override
  String get exportAll => 'تصدير الكل';

  @override
  String get archiveAll => 'أرشفة الكل';

  @override
  String get publishAllDescription => 'نشر جميع العقارات المحددة';

  @override
  String get increasePricesDescription => 'زيادة الأسعار بنسبة مئوية';

  @override
  String get decreasePricesDescription => 'تقليل الأسعار بنسبة مئوية';

  @override
  String get setPricesDescription => 'تحديد سعر ثابت لجميع العقارات';

  @override
  String get applyDiscountDescription => 'تطبيق خصم مؤقت';

  @override
  String get duplicateAllDescription => 'إنشاء نسخ من العقارات المحددة';

  @override
  String get exportAllDescription => 'تصدير بيانات العقارات';

  @override
  String get archiveAllDescription => 'أرشفة العقارات المحددة';

  @override
  String get itemsSelected => 'عناصر محددة';

  @override
  String get clearSelection => 'إلغاء التحديد';

  @override
  String get selectAll => 'تحديد الكل';

  @override
  String get moreActions => 'المزيد من الإجراءات';

  @override
  String get activateSelected => 'تفعيل المحدد';

  @override
  String get deactivateSelected => 'إلغاء تفعيل المحدد';

  @override
  String get deleteSelected => 'حذف المحدد';

  @override
  String get activateSelectedConfirmation =>
      'هل أنت متأكد من تفعيل العقارات المحددة؟';

  @override
  String get deactivateSelectedConfirmation =>
      'هل أنت متأكد من إلغاء تفعيل العقارات المحددة؟';

  @override
  String get deleteSelectedConfirmation =>
      'هل أنت متأكد من حذف العقارات المحددة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get listingsWillBeAffected => 'عقارات ستتأثر';

  @override
  String get loadingPropertyData => 'جاري تحميل بيانات العقار...';

  @override
  String get errorLoadingProperty => 'خطأ في تحميل العقار';

  @override
  String get goBack => 'العودة';

  @override
  String get propertyNotFound => 'العقار غير موجود';

  @override
  String get propertyNotFoundDescription =>
      'العقار الذي تبحث عنه غير موجود أو تم حذفه.';

  @override
  String get createNewProperty => 'إنشاء عقار جديد';

  @override
  String get propertyImages => 'صور العقار';

  @override
  String get pleaseEnterTitle => 'يرجى إدخال عنوان العقار';

  @override
  String get address => 'العنوان';

  @override
  String get saveAsDraft => 'حفظ كمسودة';

  @override
  String get updateProperty => 'تحديث العقار';

  @override
  String get propertyUpdatedSuccessfully => 'تم تحديث العقار بنجاح';

  @override
  String get propertyTitleRequired => 'عنوان العقار مطلوب';

  @override
  String get propertyTitleTooShort =>
      'عنوان العقار يجب أن يكون 3 أحرف على الأقل';

  @override
  String get propertyDescriptionRequired => 'وصف العقار مطلوب';

  @override
  String get propertyDescriptionTooShort =>
      'وصف العقار يجب أن يكون 10 أحرف على الأقل';

  @override
  String get priceRequired => 'السعر مطلوب';

  @override
  String get priceInvalid => 'يرجى إدخال سعر صحيح';

  @override
  String get priceMinimum => 'الحد الأدنى للسعر 50 ريال سعودي في الليلة';

  @override
  String get categoryRequired => 'يرجى اختيار فئة';

  @override
  String get propertyTypeRequired => 'يرجى اختيار نوع العقار';

  @override
  String get cancellationPolicyRequired => 'يرجى اختيار سياسة الإلغاء';

  @override
  String get guestsRequired => 'عدد الضيوف مطلوب';

  @override
  String get guestsInvalid => 'عدد الضيوف يجب أن يكون بين 1 و 20';

  @override
  String get bedsRequired => 'عدد غرف النوم مطلوب';

  @override
  String get bedsInvalid => 'عدد غرف النوم يجب أن يكون بين 1 و 10';

  @override
  String get bathsRequired => 'عدد الحمامات مطلوب';

  @override
  String get bathsInvalid => 'عدد الحمامات يجب أن يكون بين 1 و 10';

  @override
  String get facilitiesRequired => 'يرجى اختيار مرفق واحد على الأقل';

  @override
  String get locationRequired => 'يرجى اختيار موقع';

  @override
  String get imagesRequired => 'يرجى إضافة صورة واحدة على الأقل';

  @override
  String get imagesMinimum => 'يرجى إضافة 3 صور على الأقل';

  @override
  String get categoryAndType => 'الفئة والنوع';

  @override
  String get locationAndAddress => 'الموقع والعنوان';

  @override
  String get photosAndVideo => 'الصور والفيديو';

  @override
  String get reviewAndSubmit => 'المراجعة والإرسال';

  @override
  String get savingProperty => 'جاري حفظ العقار...';

  @override
  String get validationFailed => 'يرجى إصلاح الأخطاء والمحاولة مرة أخرى';

  @override
  String get basicInformationDesc => 'أخبرنا عن عقارك';

  @override
  String get propertyTitleHint => 'أدخل عنواناً جذاباً لعقارك';

  @override
  String get propertyDescriptionHint => 'صف عقارك بالتفصيل';

  @override
  String get priceHint => '100';

  @override
  String get priceGuidance =>
      'نصيحة: ابحث عن العقارات المماثلة في منطقتك لتحديد أسعار تنافسية';

  @override
  String get locationSelected => 'تم اختيار الموقع';

  @override
  String get changeLocation => 'تغيير الموقع';

  @override
  String get propertyPhotos => 'صور العقار';

  @override
  String get propertyVideoOptional => 'فيديو العقار (اختياري)';

  @override
  String get addPhotos => 'إضافة صور';

  @override
  String get addVideo => 'إضافة فيديو';

  @override
  String get changeVideo => 'تغيير الفيديو';

  @override
  String get takePhoto => 'التقاط صورة';

  @override
  String get chooseFromGallery => 'اختيار من المعرض';

  @override
  String get chooseMultiple => 'اختيار متعدد';

  @override
  String get noPhotosAdded => 'لم يتم إضافة صور بعد';

  @override
  String get videoPreview => 'معاينة الفيديو';

  @override
  String get main => 'رئيسي';

  @override
  String get previous => 'السابق';

  @override
  String get notProvided => 'غير مقدم';

  @override
  String get creatingProperty => 'جاري إنشاء العقار...';

  @override
  String get pleaseWaitProcessing => 'يرجى الانتظار أثناء معالجة معلوماتك';

  @override
  String get categoryTypeDescription =>
      'اختر الفئة والنوع الذي يصف عقارك بشكل أفضل';

  @override
  String get bookingRulesDescription =>
      'ضع قواعد الحجز وقدم معلومات تصريح السياحة';

  @override
  String get bookingRulesHint => 'أدخل أي قواعد محددة لحجز عقارك (اختياري)';

  @override
  String get tourismPermitNumberHint => 'أدخل رقم تصريح السياحة (مطلوب)';

  @override
  String get tourismPermitDocument => 'وثيقة تصريح السياحة *';

  @override
  String get tourismPermitDocumentHint =>
      'ارفع وثيقة تصريح السياحة (PDF, DOC, DOCX, JPG, PNG)';

  @override
  String get documentSelected => 'تم اختيار الوثيقة';

  @override
  String get changeDocument => 'تغيير الوثيقة';

  @override
  String get uploadDocument => 'رفع الوثيقة';

  @override
  String get tourismPermitInfo =>
      'تصريح السياحة مطلوب لجميع العقارات. هذا يساعد في بناء الثقة مع الضيوف ويضمن الامتثال للوائح المحلية.';

  @override
  String get propertyDetailsDescription => 'حدد تفاصيل ووسائل الراحة في عقارك';

  @override
  String get tourismPermitNumberRequired => 'رقم تصريح السياحة مطلوب';

  @override
  String get tourismPermitNumberMinLength =>
      'رقم تصريح السياحة يجب أن يكون 5 أحرف على الأقل';

  @override
  String get tourismPermitDocumentRequired => 'وثيقة تصريح السياحة مطلوبة';

  @override
  String get bookingRulesMinLength =>
      'قواعد الحجز يجب أن تكون 10 أحرف على الأقل إذا تم توفيرها';

  @override
  String get reviewDetails => 'مراجعة التفاصيل';

  @override
  String get title => 'العنوان';

  @override
  String get bookingRulesReview => 'قواعد الحجز';

  @override
  String get tourismPermitNumberReview => 'رقم تصريح السياحة';

  @override
  String get tourismPermitDocumentReview => 'وثيقة تصريح السياحة';

  @override
  String get photos => 'الصور';

  @override
  String get notSelected => 'غير محدد';

  @override
  String get noneSelected => 'لم يتم اختيار أي شيء';

  @override
  String get unknown => 'غير معروف';

  @override
  String get selected => 'محدد';

  @override
  String get uploaded => 'تم الرفع';

  @override
  String get notUploaded => 'لم يتم الرفع';

  @override
  String get added => 'تم الإضافة';

  @override
  String get notAdded => 'لم يتم الإضافة';

  @override
  String get images => 'صور';

  @override
  String get addPhoto => 'إضافة صورة';

  @override
  String get selectFacilities => 'اختيار المرافق';

  @override
  String get failedToLoadPropertyTypes => 'فشل في تحميل أنواع العقارات';

  @override
  String get failedToLoadCancellationPolicies => 'فشل في تحميل سياسات الإلغاء';

  @override
  String get loadingCategories => 'جاري تحميل الفئات...';

  @override
  String get loadingPropertyTypes => 'جاري تحميل أنواع العقارات...';

  @override
  String get loadingCancellationPolicies => 'جاري تحميل سياسات الإلغاء...';

  @override
  String get loadingFacilities => 'جاري تحميل المرافق...';

  @override
  String get propertyTypeOption => 'خيار نوع العقار';

  @override
  String get friends => 'الأصدقاء';

  @override
  String get requests => 'الطلبات';

  @override
  String get searchFriends => 'البحث';

  @override
  String get searchFriendsHint => 'ابحث عن أصدقاء بالاسم أو البريد الإلكتروني';

  @override
  String get searchForFriends => 'ابحث عن أصدقاء';

  @override
  String get searchForFriendsDescription =>
      'اكتب اسم أو بريد إلكتروني للبحث عن أصدقاء جدد';

  @override
  String get noFriendsYet => 'لا يوجد أصدقاء بعد';

  @override
  String get noFriendsDescription => 'ابدأ بإضافة أصدقاء جدد للتواصل معهم';

  @override
  String get noPendingRequests => 'لا توجد طلبات معلقة';

  @override
  String get noPendingRequestsDescription => 'ستظهر هنا طلبات الصداقة الواردة';

  @override
  String get noSearchResultsDescription =>
      'لم يتم العثور على أي مستخدمين بهذا الاسم';

  @override
  String get addFriend => 'إضافة';

  @override
  String get acceptRequest => 'قبول';

  @override
  String get declineRequest => 'رفض';

  @override
  String get removeFriend => 'إزالة صديق';

  @override
  String get alreadyFriends => 'أصدقاء';

  @override
  String get requestSent => 'تم الإرسال';

  @override
  String get host => 'مضيف';

  @override
  String get mutualFriends => 'أصدقاء مشتركين';

  @override
  String get friendRequestSentSuccess => 'تم إرسال طلب الصداقة بنجاح';

  @override
  String get friendRequestSentError => 'خطأ في إرسال طلب الصداقة';

  @override
  String get friendRequestAcceptedSuccess => 'تم قبول طلب الصداقة بنجاح';

  @override
  String get friendRequestAcceptedError => 'خطأ في قبول طلب الصداقة';

  @override
  String get friendRequestDeclinedSuccess => 'تم رفض طلب الصداقة بنجاح';

  @override
  String get friendRequestDeclinedError => 'خطأ في رفض طلب الصداقة';

  @override
  String get friendRemovedSuccess => 'تم إزالة الصديق بنجاح';

  @override
  String get friendRemovedError => 'خطأ في إزالة الصديق';

  @override
  String get searchError => 'خطأ في البحث';

  @override
  String get loadingFriends => 'جاري تحميل الأصدقاء...';

  @override
  String get loadingRequests => 'جاري تحميل الطلبات...';

  @override
  String get refreshFriends => 'تحديث قائمة الأصدقاء';

  @override
  String get goToPendingRequests =>
      'يرجى الذهاب إلى تبويب الطلبات لقبول طلب الصداقة';

  @override
  String get messageFeatureInDevelopment => 'ميزة الرسائل قيد التطوير';

  @override
  String get failedToLoadFriends => 'فشل في تحميل الأصدقاء';

  @override
  String get failedToLoadRequests => 'فشل في تحميل الطلبات';

  @override
  String get acquaintances => 'المعارف';

  @override
  String get sendMessage => 'إرسال رسالة';

  @override
  String get friendRequestTime => 'طلب الصداقة';

  @override
  String get retryButton => 'إعادة المحاولة';

  @override
  String get minutesAgo => 'دقيقة';

  @override
  String get hoursAgo => 'ساعة';

  @override
  String get daysAgo => 'يوم';

  @override
  String get ago => 'منذ';

  @override
  String get hostModeActivated => 'تم تفعيل وضع المضيف بنجاح';

  @override
  String get hostModeDeactivated => 'تم إلغاء تفعيل وضع المضيف بنجاح';

  @override
  String get earlyAccessFeatures => 'مميزات برنامج الوصول المبكر';

  @override
  String get newLabel => 'الجديد';

  @override
  String get guestUser => 'مستخدم ضيف';

  @override
  String get loginForFullExperience => 'سجل دخولك للحصول على تجربة كاملة';

  @override
  String get jeddahSaudiArabia => 'جدة، المملكة العربية السعودية';

  @override
  String get loginRequired => 'تسجيل الدخول مطلوب';

  @override
  String get loginRequiredMessage => 'يجب تسجيل الدخول للوصول إلى هذه الميزة';

  @override
  String get switchToTravel => 'التبديل الي السفر';

  @override
  String get hosting => 'الاستضافة';

  @override
  String get editProfileTitle => 'تعديل الملف الشخصي';

  @override
  String get saveChangesTooltip => 'حفظ التغييرات';

  @override
  String get unexpectedError => 'حدث خطأ غير متوقع';

  @override
  String get pleaseSelectBirthdate => 'يرجى اختيار تاريخ الميلاد';

  @override
  String get validationError => 'خطأ في التحقق من البيانات';

  @override
  String get accountSettingsTitle => 'إعدادات الحساب';

  @override
  String get loginRequiredForSettings =>
      'يجب تسجيل الدخول للوصول إلى إعدادات الحساب';

  @override
  String get accountInfo => 'معلومات الحساب';

  @override
  String get editProfileSubtitle => 'تحديث الاسم والصورة والمعلومات الشخصية';

  @override
  String get emailAddress => 'البريد الإلكتروني';

  @override
  String get phoneNumberLabel => 'رقم الهاتف';

  @override
  String get security => 'الأمان';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get changePasswordSubtitle => 'تحديث كلمة المرور الخاصة بك';

  @override
  String get twoFactorAuth => 'المصادقة الثنائية';

  @override
  String get twoFactorAuthSubtitle => 'تأمين إضافي لحسابك';

  @override
  String get connectedDevices => 'الأجهزة المتصلة';

  @override
  String get connectedDevicesSubtitle =>
      'إدارة الأجهزة التي تم تسجيل الدخول منها';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get notificationsSubtitle => 'إدارة إعدادات الإشعارات';

  @override
  String get locationSubtitle => 'إعدادات الخصوصية والموقع';

  @override
  String get dangerZone => 'منطقة الخطر';

  @override
  String get deleteAccountSubtitle => 'حذف حسابك نهائياً - لا يمكن التراجع';

  @override
  String get deleteAccountTitle => 'حذف الحساب';

  @override
  String get deleteAccountConfirmMessage =>
      'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع بياناتك.';

  @override
  String get delete => 'حذف';

  @override
  String get supportCenter => 'مركز الدعم';

  @override
  String get errorOccurred => 'خطأ';

  @override
  String get frequentlyAskedQuestions => 'الأسئلة الشائعة';

  @override
  String get supportTickets => 'تذاكر الدعم';

  @override
  String get newTicket => 'تذكرة جديدة';

  @override
  String get noFaqsAvailable => 'لا توجد أسئلة شائعة متاحة';

  @override
  String get noSupportTickets => 'لا توجد تذاكر دعم';

  @override
  String get ticketCreatedSuccessfully => 'تم إنشاء التذكرة بنجاح';

  @override
  String get createNewSupportTicket => 'إنشاء تذكرة دعم جديدة';

  @override
  String get subject => 'الموضوع';

  @override
  String get pleaseEnterSubject => 'يرجى إدخال الموضوع';

  @override
  String get descriptionLabel => 'الوصف';

  @override
  String get priority => 'الأولوية';

  @override
  String get low => 'منخفض';

  @override
  String get medium => 'متوسط';

  @override
  String get high => 'عالي';

  @override
  String get urgent => 'عاجل';

  @override
  String get submitTicket => 'إرسال التذكرة';

  @override
  String get helpAndSupport => 'المساعدة والدعم';

  @override
  String get quickHelp => 'مساعدة سريعة';

  @override
  String get howToSearch => 'كيفية البحث';

  @override
  String get makeBooking => 'إجراء حجز';

  @override
  String get paymentAndBilling => 'الدفع والفواتير';

  @override
  String get frequentQuestions => 'الأسئلة الشائعة';

  @override
  String get howToCancelBooking => 'كيف يمكنني إلغاء حجز؟';

  @override
  String get howToCancelBookingAnswer =>
      'يمكنك إلغاء الحجز من خلال الذهاب إلى \"حجوزاتي\" واختيار الحجز المراد إلغاؤه.';

  @override
  String get refundPolicy => 'ما هي سياسة الاسترداد؟';

  @override
  String get refundPolicyAnswer =>
      'تختلف سياسة الاسترداد حسب نوع العقار وسياسة المضيف. يمكنك مراجعة التفاصيل في صفحة الحجز.';

  @override
  String get howToChangeBooking => 'كيف يمكنني تغيير معلومات الحجز؟';

  @override
  String get howToChangeBookingAnswer =>
      'يمكنك تعديل بعض معلومات الحجز من خلال التواصل مع المضيف أو خدمة العملاء.';

  @override
  String get howToBecomeHost => 'كيف أصبح مضيف؟';

  @override
  String get howToBecomeHostAnswer =>
      'يمكنك التبديل إلى وضع المضيف من الملف الشخصي وإضافة عقارك الأول.';

  @override
  String get liveChat => 'الدردشة المباشرة';

  @override
  String get available24_7 => 'متاح 24/7';

  @override
  String get emailSupport => 'البريد الإلكتروني';

  @override
  String get phoneSupport => 'الهاتف';

  @override
  String get usefulResources => 'مصادر مفيدة';

  @override
  String get userGuide => 'دليل المستخدم';

  @override
  String get tutorialVideos => 'فيديوهات تعليمية';

  @override
  String get helpCenter => 'مركز المساعدة';

  @override
  String get viewProfileTitle => 'عرض الملف الشخصي';

  @override
  String get loginRequiredToViewProfile => 'يجب تسجيل الدخول لعرض الملف الشخصي';

  @override
  String get menu => 'القائمة';

  @override
  String get switchToTravelMode => 'التبديل إلى السفر';

  @override
  String get trips => 'الرحلات';

  @override
  String get aboutMe => 'نبذة عني';

  @override
  String get noAboutMeAdded => 'لم يتم إضافة نبذة شخصية بعد.';

  @override
  String get languages => 'اللغات';

  @override
  String get arabicEnglish => 'العربية، الإنجليزية';

  @override
  String get locationLabel => 'الموقع';

  @override
  String get memberSince => 'عضو منذ';

  @override
  String get january2023 => 'يناير 2023';

  @override
  String get verification => 'التحقق';

  @override
  String get emailVerification => 'البريد الإلكتروني';

  @override
  String get phoneVerification => 'رقم الهاتف';

  @override
  String get identityVerification => 'الهوية الشخصية';

  @override
  String get manageNotificationSettings => 'إدارة إعدادات الإشعارات';

  @override
  String get privacyLocationSettings => 'إعدادات الخصوصية والموقع';

  @override
  String get faq => 'الأسئلة الشائعة';

  @override
  String get errorMessage => 'خطأ';

  @override
  String get cancelBookingAnswer =>
      'يمكنك إلغاء الحجز من خلال الذهاب إلى \"حجوزاتي\" واختيار الحجز المراد إلغاؤه.';

  @override
  String get changeBookingInfo => 'كيف يمكنني تغيير معلومات الحجز؟';

  @override
  String get changeBookingAnswer =>
      'يمكنك تعديل بعض معلومات الحجز من خلال التواصل مع المضيف أو خدمة العملاء.';

  @override
  String get becomeHost => 'كيف أصبح مضيف؟';

  @override
  String get becomeHostAnswer =>
      'يمكنك التبديل إلى وضع المضيف من الملف الشخصي وإضافة عقارك الأول.';

  @override
  String get available247 => 'متاح 24/7';

  @override
  String get jeddahSaudi => 'جدة، المملكة العربية السعودية';

  @override
  String get noBioAdded => 'لم يتم إضافة نبذة شخصية بعد.';

  @override
  String get rating0 => 'لا توجد تقييمات';

  @override
  String get accountInfoTitle => 'معلومات الحساب';

  @override
  String get personalInfoTab => 'المعلومات الشخصية';

  @override
  String get securityTab => 'الأمان';

  @override
  String get statisticsTab => 'الإحصائيات';

  @override
  String get noInfoAvailable => 'لا توجد معلومات متاحة';

  @override
  String get noSecuritySettingsAvailable => 'لا توجد إعدادات أمان متاحة';

  @override
  String get noStatisticsAvailable => 'لا توجد إحصائيات متاحة';

  @override
  String get editPersonalInfo => 'تعديل المعلومات الشخصية';

  @override
  String get exportAccountData => 'تصدير بيانات الحساب';

  @override
  String get activeSessions => 'الجلسات النشطة';

  @override
  String get recentActivities => 'النشاطات الأخيرة';

  @override
  String get accountStatistics => 'إحصائيات الحساب';

  @override
  String get memberSinceLabel => 'عضو منذ';

  @override
  String get lastLogin => 'آخر دخول';

  @override
  String get totalBookingsLabel => 'إجمالي الحجوزات';

  @override
  String get totalReviewsLabel => 'إجمالي التقييمات';

  @override
  String get verifiedAccount => 'حساب موثق';

  @override
  String get unverifiedAccount => 'حساب غير موثق';

  @override
  String get currentSession => 'الحالي';

  @override
  String get dataExportedSuccessfully => 'تم تصدير البيانات';

  @override
  String get dataExportSuccessMessage => 'تم تصدير بياناتك بنجاح';

  @override
  String get bookingsCount => 'حجز';

  @override
  String get completePersonalInfo => 'معلومات شخصية كاملة';

  @override
  String get cannotLoadUserData =>
      'لا يمكن تحميل بيانات المستخدم. يرجى المحاولة مرة أخرى.';

  @override
  String get changePasswordTitle => 'تغيير كلمة المرور';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmNewPassword => 'تأكيد كلمة المرور الجديدة';

  @override
  String get enterCurrentPassword => 'يرجى إدخال كلمة المرور الحالية';

  @override
  String get enterNewPassword => 'يرجى إدخال كلمة المرور الجديدة';

  @override
  String get passwordMinLength => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';

  @override
  String get confirmNewPasswordField => 'يرجى تأكيد كلمة المرور الجديدة';

  @override
  String get passwordsDoNotMatch => 'كلمة المرور غير متطابقة';

  @override
  String get changePasswordButton => 'تغيير كلمة المرور';

  @override
  String get passwordChangedSuccessfully => 'تم تغيير كلمة المرور بنجاح';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get hostingResources => 'موارد الاستضافة';

  @override
  String get findCoHost => 'العثور على مضيف مشارك';

  @override
  String get createNewListing => 'إنشاء إعلان جديد';

  @override
  String get privacySettingsTitle => 'إعدادات الخصوصية';

  @override
  String get profilePrivacy => 'خصوصية الملف الشخصي';

  @override
  String get showProfile => 'إظهار الملف الشخصي';

  @override
  String get showProfileSubtitle => 'السماح للآخرين برؤية ملفك الشخصي';

  @override
  String get showEmail => 'إظهار البريد الإلكتروني';

  @override
  String get showEmailSubtitle => 'عرض بريدك الإلكتروني في الملف الشخصي';

  @override
  String get showPhone => 'إظهار رقم الهاتف';

  @override
  String get showPhoneSubtitle => 'عرض رقم هاتفك في الملف الشخصي';

  @override
  String get dataAndLocation => 'البيانات والموقع';

  @override
  String get shareLocation => 'مشاركة الموقع';

  @override
  String get shareLocationSubtitle => 'السماح للتطبيق بالوصول إلى موقعك';

  @override
  String get analyticsDataCollection => 'جمع البيانات التحليلية';

  @override
  String get analyticsDataCollectionSubtitle =>
      'مساعدتنا في تحسين التطبيق من خلال البيانات المجهولة';

  @override
  String get downloadMyData => 'تحميل بياناتي';

  @override
  String get downloadMyDataSubtitle => 'احصل على نسخة من جميع بياناتك';

  @override
  String get communication => 'التواصل';

  @override
  String get allowMessages => 'السماح بالرسائل';

  @override
  String get allowMessagesSubtitle => 'السماح للمضيفين والضيوف بإرسال رسائل لك';

  @override
  String get pushNotificationsSubtitle =>
      'تلقي إشعارات فورية للرسائل والحجوزات';

  @override
  String get marketingEmails => 'رسائل التسويق';

  @override
  String get marketingEmailsSubtitle =>
      'تلقي رسائل بريد إلكتروني حول العروض والأخبار';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get clearSearchHistory => 'مسح سجل البحث';

  @override
  String get clearSearchHistorySubtitle => 'حذف جميع عمليات البحث السابقة';

  @override
  String get clearCache => 'مسح البيانات المؤقتة';

  @override
  String get clearCacheSubtitle => 'حذف الملفات المؤقتة والصور المحفوظة';

  @override
  String get blockedUsers => 'المستخدمون المحظورون';

  @override
  String get blockedUsersSubtitle => 'إدارة قائمة المستخدمين المحظورين';

  @override
  String get downloadData => 'تحميل البيانات';

  @override
  String get downloadDataMessage =>
      'سيتم إرسال نسخة من جميع بياناتك إلى بريدك الإلكتروني خلال 24 ساعة.';

  @override
  String get downloadDataSuccess => 'تم طلب تحميل البيانات بنجاح';

  @override
  String get clearSearchHistoryTitle => 'مسح سجل البحث';

  @override
  String get clearSearchHistoryMessage =>
      'هل تريد حذف جميع عمليات البحث السابقة؟';

  @override
  String get searchHistoryCleared => 'تم مسح سجل البحث';

  @override
  String get clearCacheTitle => 'مسح البيانات المؤقتة';

  @override
  String get clearCacheMessage => 'هل تريد حذف جميع الملفات المؤقتة؟';

  @override
  String get cacheCleared => 'تم مسح البيانات المؤقتة';

  @override
  String get clear => 'مسح';

  @override
  String get logoutSuccessful => 'تم تسجيل الخروج بنجاح';

  @override
  String get chooseWhatToDoNow => 'اختر ما تريد فعله الآن';

  @override
  String get reviewsLoadError => 'خطأ في تحميل التقييمات';

  @override
  String get highestRated => 'الأعلى تقييماً';

  @override
  String get lowestRated => 'الأقل تقييماً';

  @override
  String get minRating => 'الحد الأدنى للتقييم';

  @override
  String get fourPlusStars => '4+ نجوم';

  @override
  String get threePlusStars => '3+ نجوم';

  @override
  String get twoPlusStars => '2+ نجوم';

  @override
  String get onePlusStars => '1+ نجوم';

  @override
  String get noReviews => 'لا توجد تقييمات';

  @override
  String get beFirstToReview => 'كن أول من يقيم هذا العقار';
}
