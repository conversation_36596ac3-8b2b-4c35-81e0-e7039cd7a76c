// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(price) => "${price} ر.س";

  static String m1(percent) =>
      "هذا المسكن من أفضل ${percent}% من المساكن المماثلة، استناداً إلى التقييمات والمراجعات وبيانات الموثوقية";

  static String m2(count) => "عرض جميع التقييمات البالغ عددها ${count} تقييمًا";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutMe": MessageLookupByLibrary.simpleMessage("نبذة عني"),
        "aboutThisPlace": MessageLookupByLibrary.simpleMessage("عن هذا المكان"),
        "acceptRequest": MessageLookupByLibrary.simpleMessage("قبول"),
        "accessibility": MessageLookupByLibrary.simpleMessage("إمكانية الوصول"),
        "accountInfo": MessageLookupByLibrary.simpleMessage("معلومات الحساب"),
        "accountInfoTitle":
            MessageLookupByLibrary.simpleMessage("معلومات الحساب"),
        "accountSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الحساب"),
        "accountSettingsTitle":
            MessageLookupByLibrary.simpleMessage("إعدادات الحساب"),
        "accountStatistics":
            MessageLookupByLibrary.simpleMessage("إحصائيات الحساب"),
        "acquaintances": MessageLookupByLibrary.simpleMessage("المعارف"),
        "actionParameters":
            MessageLookupByLibrary.simpleMessage("معاملات الإجراء"),
        "activate": MessageLookupByLibrary.simpleMessage("تفعيل"),
        "activateAll": MessageLookupByLibrary.simpleMessage("تفعيل الكل"),
        "activateAllDescription": MessageLookupByLibrary.simpleMessage(
            "جعل جميع العقارات المحددة مرئية للضيوف"),
        "activateSelected":
            MessageLookupByLibrary.simpleMessage("تفعيل المحدد"),
        "activateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من تفعيل العقارات المحددة؟"),
        "active": MessageLookupByLibrary.simpleMessage("نشطة"),
        "activeListings":
            MessageLookupByLibrary.simpleMessage("العقارات النشطة"),
        "activeSessions":
            MessageLookupByLibrary.simpleMessage("الجلسات النشطة"),
        "activeStatusDescription":
            MessageLookupByLibrary.simpleMessage("عقارك مُفعل ومرئي للضيوف"),
        "addFriend": MessageLookupByLibrary.simpleMessage("إضافة"),
        "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addImages": MessageLookupByLibrary.simpleMessage("إضافة صور"),
        "addNewListing":
            MessageLookupByLibrary.simpleMessage("إضافة إعلان جديد"),
        "addPhoto": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addPhotos": MessageLookupByLibrary.simpleMessage("إضافة صور"),
        "addProperty": MessageLookupByLibrary.simpleMessage("إضافة عقار"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("أضف إلى المفضلة"),
        "addVideo": MessageLookupByLibrary.simpleMessage("إضافة فيديو"),
        "added": MessageLookupByLibrary.simpleMessage("تم الإضافة"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("معلومات إضافية"),
        "additionalSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات إضافية"),
        "address": MessageLookupByLibrary.simpleMessage("العنوان"),
        "advancedBulkActions":
            MessageLookupByLibrary.simpleMessage("إجراءات جماعية متقدمة"),
        "ago": MessageLookupByLibrary.simpleMessage("منذ"),
        "airConditioning": MessageLookupByLibrary.simpleMessage("تكييف"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "allCategories": MessageLookupByLibrary.simpleMessage("جميع الفئات"),
        "allListings": MessageLookupByLibrary.simpleMessage("جميع العقارات"),
        "allReviews": MessageLookupByLibrary.simpleMessage("الكل"),
        "allowMessages":
            MessageLookupByLibrary.simpleMessage("السماح بالرسائل"),
        "allowMessagesSubtitle": MessageLookupByLibrary.simpleMessage(
            "السماح للمضيفين والضيوف بإرسال رسائل لك"),
        "alreadyFriends": MessageLookupByLibrary.simpleMessage("أصدقاء"),
        "amenities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "analytics": MessageLookupByLibrary.simpleMessage("التحليلات"),
        "analyticsDataCollection":
            MessageLookupByLibrary.simpleMessage("جمع البيانات التحليلية"),
        "analyticsDataCollectionSubtitle": MessageLookupByLibrary.simpleMessage(
            "مساعدتنا في تحسين التطبيق من خلال البيانات المجهولة"),
        "analyticsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على التحليلات"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "تطبيق لحجز العقارات والأماكن"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appInformation":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appName": MessageLookupByLibrary.simpleMessage("نقطة تجمع"),
        "appearance": MessageLookupByLibrary.simpleMessage("المظهر"),
        "applyAction": MessageLookupByLibrary.simpleMessage("تطبيق الإجراء"),
        "applyDiscount": MessageLookupByLibrary.simpleMessage("تطبيق خصم"),
        "applyDiscountDescription":
            MessageLookupByLibrary.simpleMessage("تطبيق خصم مؤقت"),
        "applyFilter": MessageLookupByLibrary.simpleMessage("تطبيق التصفية"),
        "applyFilters": MessageLookupByLibrary.simpleMessage("تطبيق"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "arabicEnglish":
            MessageLookupByLibrary.simpleMessage("العربية، الإنجليزية"),
        "archiveAll": MessageLookupByLibrary.simpleMessage("أرشفة الكل"),
        "archiveAllDescription":
            MessageLookupByLibrary.simpleMessage("أرشفة العقارات المحددة"),
        "available247": MessageLookupByLibrary.simpleMessage("متاح 24/7"),
        "available24_7": MessageLookupByLibrary.simpleMessage("متاح 24/7"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد المتاح"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("الخدمات المتوفرة"),
        "average": MessageLookupByLibrary.simpleMessage("متوسط"),
        "averageDailyRate":
            MessageLookupByLibrary.simpleMessage("متوسط السعر اليومي"),
        "averageRating": MessageLookupByLibrary.simpleMessage("متوسط التقييم"),
        "back": MessageLookupByLibrary.simpleMessage("رجوع"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("العودة للبحث"),
        "balcony": MessageLookupByLibrary.simpleMessage("شرفة"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("تحويل بنكي"),
        "bar": MessageLookupByLibrary.simpleMessage("بار"),
        "basicInformation":
            MessageLookupByLibrary.simpleMessage("المعلومات الأساسية"),
        "basicInformationDesc":
            MessageLookupByLibrary.simpleMessage("أخبرنا عن عقارك"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("الحمامات"),
        "bathsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد الحمامات يجب أن يكون بين 1 و 10"),
        "bathsRequired":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات مطلوب"),
        "beFirstToReview":
            MessageLookupByLibrary.simpleMessage("كن أول من يقيم هذا العقار"),
        "beFirstToReviewThisPlace":
            MessageLookupByLibrary.simpleMessage("كن أول من يقيم هذا المكان"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("وصول للشاطئ"),
        "becomeHost": MessageLookupByLibrary.simpleMessage("كيف أصبح مضيف؟"),
        "becomeHostAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك التبديل إلى وضع المضيف من الملف الشخصي وإضافة عقارك الأول."),
        "bedrooms": MessageLookupByLibrary.simpleMessage("غرف النوم"),
        "bedsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد غرف النوم يجب أن يكون بين 1 و 10"),
        "bedsRequired":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم مطلوب"),
        "bio": MessageLookupByLibrary.simpleMessage("الوصف"),
        "birthdate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("المستخدمون المحظورون"),
        "blockedUsersSubtitle": MessageLookupByLibrary.simpleMessage(
            "إدارة قائمة المستخدمين المحظورين"),
        "bookNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("تاريخ الحجز"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل وسياسات الحجز"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("رسوم الحجز"),
        "bookingMetrics": MessageLookupByLibrary.simpleMessage("مقاييس الحجز"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("سياسة الحجز"),
        "bookingRules": MessageLookupByLibrary.simpleMessage("قواعد الحجز"),
        "bookingRulesDescription": MessageLookupByLibrary.simpleMessage(
            "ضع قواعد الحجز وقدم معلومات تصريح السياحة"),
        "bookingRulesHint": MessageLookupByLibrary.simpleMessage(
            "أدخل أي قواعد محددة لحجز عقارك (اختياري)"),
        "bookingRulesMinLength": MessageLookupByLibrary.simpleMessage(
            "قواعد الحجز يجب أن تكون 10 أحرف على الأقل إذا تم توفيرها"),
        "bookingRulesReview":
            MessageLookupByLibrary.simpleMessage("قواعد الحجز"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("حالة الحجز"),
        "bookingSummary": MessageLookupByLibrary.simpleMessage("ملخص الحجز"),
        "bookingWindow":
            MessageLookupByLibrary.simpleMessage("مهلة ما بعد الحجز"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("مخطط الحجوزات"),
        "bookingsCount": MessageLookupByLibrary.simpleMessage("حجز"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الحجوزات"),
        "browseReels": MessageLookupByLibrary.simpleMessage("تصفح الريلز"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("صديق للميزانية"),
        "bulkActionCompleted": MessageLookupByLibrary.simpleMessage(
            "تم تنفيذ الإجراء الجماعي بنجاح"),
        "bulkActionError": MessageLookupByLibrary.simpleMessage(
            "خطأ في تنفيذ الإجراء الجماعي"),
        "bulkActions": MessageLookupByLibrary.simpleMessage("إجراءات جماعية"),
        "cacheCleared":
            MessageLookupByLibrary.simpleMessage("تم مسح البيانات المؤقتة"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancelBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك إلغاء الحجز من خلال الذهاب إلى \"حجوزاتي\" واختيار الحجز المراد إلغاؤه."),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancelSelection":
            MessageLookupByLibrary.simpleMessage("إلغاء التحديد"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("سياسة الإلغاء"),
        "cancellationPolicyNote": MessageLookupByLibrary.simpleMessage(
            "تذكر أن السياسة التي يضعها المضيف تناسب ظروفك، في حالات نادرة قد تكون مؤهلاً لاسترداد جزئي أو كامل وفقاً لسياسة الموقع."),
        "cancellationPolicyRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار سياسة الإلغاء"),
        "cancellationRate":
            MessageLookupByLibrary.simpleMessage("معدل الإلغاء"),
        "cancellationRules":
            MessageLookupByLibrary.simpleMessage("قواعد الإلغاء"),
        "cancellationWindow":
            MessageLookupByLibrary.simpleMessage("مهلة الإلغاء"),
        "cancelled": MessageLookupByLibrary.simpleMessage("ملغية"),
        "cancelledBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات الملغية"),
        "cannotLoadUserData": MessageLookupByLibrary.simpleMessage(
            "لا يمكن تحميل بيانات المستخدم. يرجى المحاولة مرة أخرى."),
        "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
        "category": MessageLookupByLibrary.simpleMessage("الفئة"),
        "categoryAndType": MessageLookupByLibrary.simpleMessage("الفئة والنوع"),
        "categoryRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار فئة"),
        "categoryTypeDescription": MessageLookupByLibrary.simpleMessage(
            "اختر الفئة والنوع الذي يصف عقارك بشكل أفضل"),
        "change": MessageLookupByLibrary.simpleMessage("تغيير"),
        "changeBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك تعديل بعض معلومات الحجز من خلال التواصل مع المضيف أو خدمة العملاء."),
        "changeBookingInfo": MessageLookupByLibrary.simpleMessage(
            "كيف يمكنني تغيير معلومات الحجز؟"),
        "changeDocument": MessageLookupByLibrary.simpleMessage("تغيير الوثيقة"),
        "changeListingStatus":
            MessageLookupByLibrary.simpleMessage("تغيير حالة العقار"),
        "changeLocation": MessageLookupByLibrary.simpleMessage("تغيير الموقع"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "changePasswordButton":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "changePasswordSubtitle":
            MessageLookupByLibrary.simpleMessage("تحديث كلمة المرور الخاصة بك"),
        "changePasswordTitle":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "changeReason": MessageLookupByLibrary.simpleMessage("سبب التغيير"),
        "changeStatus": MessageLookupByLibrary.simpleMessage("تغيير الحالة"),
        "changeVideo": MessageLookupByLibrary.simpleMessage("تغيير الفيديو"),
        "charts": MessageLookupByLibrary.simpleMessage("الرسوم البيانية"),
        "checkConnection":
            MessageLookupByLibrary.simpleMessage("تحقق من اتصالك بالإنترنت"),
        "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الوصول"),
        "checkInDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل الوصول"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("تعليمات تسجيل الوصول"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("الوصول/المغادرة"),
        "checkInternetConnection":
            MessageLookupByLibrary.simpleMessage("تحقق من اتصال الإنترنت"),
        "checkOut": MessageLookupByLibrary.simpleMessage("تسجيل المغادرة"),
        "checkOutDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل المغادرة"),
        "chooseFromGallery":
            MessageLookupByLibrary.simpleMessage("اختيار من المعرض"),
        "chooseMultiple": MessageLookupByLibrary.simpleMessage("اختيار متعدد"),
        "chooseWhatToDoNow":
            MessageLookupByLibrary.simpleMessage("اختر ما تريد فعله الآن"),
        "cityView": MessageLookupByLibrary.simpleMessage("إطلالة المدينة"),
        "cleaningFeeNotRefundable": MessageLookupByLibrary.simpleMessage(
            "رسوم التنظيف غير قابلة للاسترداد"),
        "cleaningFeeRefundable": MessageLookupByLibrary.simpleMessage(
            "رسوم التنظيف قابلة للاسترداد"),
        "clear": MessageLookupByLibrary.simpleMessage("مسح"),
        "clearCache":
            MessageLookupByLibrary.simpleMessage("مسح البيانات المؤقتة"),
        "clearCacheMessage": MessageLookupByLibrary.simpleMessage(
            "هل تريد حذف جميع الملفات المؤقتة؟"),
        "clearCacheSubtitle": MessageLookupByLibrary.simpleMessage(
            "حذف الملفات المؤقتة والصور المحفوظة"),
        "clearCacheTitle":
            MessageLookupByLibrary.simpleMessage("مسح البيانات المؤقتة"),
        "clearFilter": MessageLookupByLibrary.simpleMessage("مسح التصفية"),
        "clearFilters": MessageLookupByLibrary.simpleMessage("مسح الفلاتر"),
        "clearSearchHistory":
            MessageLookupByLibrary.simpleMessage("مسح سجل البحث"),
        "clearSearchHistoryMessage": MessageLookupByLibrary.simpleMessage(
            "هل تريد حذف جميع عمليات البحث السابقة؟"),
        "clearSearchHistorySubtitle": MessageLookupByLibrary.simpleMessage(
            "حذف جميع عمليات البحث السابقة"),
        "clearSearchHistoryTitle":
            MessageLookupByLibrary.simpleMessage("مسح سجل البحث"),
        "clearSelection": MessageLookupByLibrary.simpleMessage("إلغاء التحديد"),
        "codeExpired":
            MessageLookupByLibrary.simpleMessage("انتهت صلاحية الكود"),
        "comment": MessageLookupByLibrary.simpleMessage("تعليق"),
        "commentFailed":
            MessageLookupByLibrary.simpleMessage("فشل في نشر التعليق"),
        "commentPosted":
            MessageLookupByLibrary.simpleMessage("تم نشر التعليق بنجاح"),
        "comments": MessageLookupByLibrary.simpleMessage("التعليقات"),
        "commission": MessageLookupByLibrary.simpleMessage("عمولة"),
        "communication": MessageLookupByLibrary.simpleMessage("التواصل"),
        "completePersonalInfo":
            MessageLookupByLibrary.simpleMessage("معلومات شخصية كاملة"),
        "completed": MessageLookupByLibrary.simpleMessage("مكتملة"),
        "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
        "confirmBooking": MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmLocation": MessageLookupByLibrary.simpleMessage("تأكيد الموقع"),
        "confirmNewPassword":
            MessageLookupByLibrary.simpleMessage("تأكيد كلمة المرور الجديدة"),
        "confirmNewPasswordField": MessageLookupByLibrary.simpleMessage(
            "يرجى تأكيد كلمة المرور الجديدة"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("تأكيد الإرسال"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إرسال هذا العقار؟"),
        "confirmed": MessageLookupByLibrary.simpleMessage("مؤكدة"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات المؤكدة"),
        "connectedDevices":
            MessageLookupByLibrary.simpleMessage("الأجهزة المتصلة"),
        "connectedDevicesSubtitle": MessageLookupByLibrary.simpleMessage(
            "إدارة الأجهزة التي تم تسجيل الدخول منها"),
        "connectionError":
            MessageLookupByLibrary.simpleMessage("خطأ في الاتصال"),
        "contactHost": MessageLookupByLibrary.simpleMessage("تواصل مع المضيف"),
        "contactInfo": MessageLookupByLibrary.simpleMessage("معلومات التواصل"),
        "contactSupport":
            MessageLookupByLibrary.simpleMessage("تواصل مع الدعم"),
        "contactUs": MessageLookupByLibrary.simpleMessage("تواصل معنا"),
        "contactUsDesc":
            MessageLookupByLibrary.simpleMessage("تواصل مع فريق الدعم لدينا"),
        "continueAsGuest":
            MessageLookupByLibrary.simpleMessage("المتابعة كضيف"),
        "continueButton": MessageLookupByLibrary.simpleMessage("المتابعة"),
        "conversionRate": MessageLookupByLibrary.simpleMessage("معدل التحويل"),
        "convertToDraft":
            MessageLookupByLibrary.simpleMessage("تحويل إلى مسودة"),
        "convertToDraftDescription": MessageLookupByLibrary.simpleMessage(
            "تحويل جميع العقارات المحددة إلى حالة مسودة"),
        "createFirstListing":
            MessageLookupByLibrary.simpleMessage("إنشاء أول عقار"),
        "createNewListing":
            MessageLookupByLibrary.simpleMessage("إنشاء إعلان جديد"),
        "createNewProperty":
            MessageLookupByLibrary.simpleMessage("إنشاء عقار جديد"),
        "createNewSupportTicket":
            MessageLookupByLibrary.simpleMessage("إنشاء تذكرة دعم جديدة"),
        "createProperty": MessageLookupByLibrary.simpleMessage("إنشاء عقار"),
        "creatingProperty":
            MessageLookupByLibrary.simpleMessage("جاري إنشاء العقار..."),
        "currencyCode": MessageLookupByLibrary.simpleMessage("ر.س"),
        "currencySymbol": MessageLookupByLibrary.simpleMessage("ر.س"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("الموقع الحالي"),
        "currentPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور الحالية"),
        "currentSession": MessageLookupByLibrary.simpleMessage("الحالي"),
        "currentStatus": MessageLookupByLibrary.simpleMessage("الحالة الحالية"),
        "currentStatusBreakdown":
            MessageLookupByLibrary.simpleMessage("تفصيل الحالة الحالية"),
        "customizeExperience":
            MessageLookupByLibrary.simpleMessage("خصص تجربتك في التطبيق"),
        "dailyBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات اليومية"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("السعر اليومي"),
        "dailyRevenue":
            MessageLookupByLibrary.simpleMessage("الإيرادات اليومية"),
        "dailyViews": MessageLookupByLibrary.simpleMessage("المشاهدات اليومية"),
        "dangerZone": MessageLookupByLibrary.simpleMessage("منطقة الخطر"),
        "darkMode": MessageLookupByLibrary.simpleMessage("الوضع المظلم"),
        "dashboardOverview": MessageLookupByLibrary.simpleMessage("نظرة عامة"),
        "dataAndLocation":
            MessageLookupByLibrary.simpleMessage("البيانات والموقع"),
        "dataAndPrivacy":
            MessageLookupByLibrary.simpleMessage("البيانات والخصوصية"),
        "dataCollection": MessageLookupByLibrary.simpleMessage("جمع البيانات"),
        "dataCollectionDesc":
            MessageLookupByLibrary.simpleMessage("كيف نجمع ونستخدم بياناتك"),
        "dataExportSuccessMessage":
            MessageLookupByLibrary.simpleMessage("تم تصدير بياناتك بنجاح"),
        "dataExportedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تصدير البيانات"),
        "dataLoadError":
            MessageLookupByLibrary.simpleMessage("حدث خطأ في تحميل البيانات"),
        "dataLoadFailed":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل البيانات"),
        "dataManagement":
            MessageLookupByLibrary.simpleMessage("إدارة البيانات"),
        "dataRetention":
            MessageLookupByLibrary.simpleMessage("الاحتفاظ بالبيانات"),
        "dataRetentionDesc":
            MessageLookupByLibrary.simpleMessage("كم من الوقت نحتفظ ببياناتك"),
        "dates": MessageLookupByLibrary.simpleMessage("التواريخ"),
        "days": MessageLookupByLibrary.simpleMessage("أيام"),
        "daysAgo": MessageLookupByLibrary.simpleMessage("يوم"),
        "deactivate": MessageLookupByLibrary.simpleMessage("إلغاء تفعيل"),
        "deactivateAll":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل الكل"),
        "deactivateAllDescription": MessageLookupByLibrary.simpleMessage(
            "إخفاء جميع العقارات المحددة عن الضيوف"),
        "deactivateListing":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل العقار"),
        "deactivateListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إلغاء تفعيل هذا العقار؟"),
        "deactivateSelected":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل المحدد"),
        "deactivateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إلغاء تفعيل العقارات المحددة؟"),
        "declineRequest": MessageLookupByLibrary.simpleMessage("رفض"),
        "decreasePrices": MessageLookupByLibrary.simpleMessage("تقليل الأسعار"),
        "decreasePricesDescription":
            MessageLookupByLibrary.simpleMessage("تقليل الأسعار بنسبة مئوية"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
        "deleteAccountConfirmMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع بياناتك."),
        "deleteAccountDesc":
            MessageLookupByLibrary.simpleMessage("حذف حسابك وبياناتك نهائياً"),
        "deleteAccountSubtitle": MessageLookupByLibrary.simpleMessage(
            "حذف حسابك نهائياً - لا يمكن التراجع"),
        "deleteAccountTitle":
            MessageLookupByLibrary.simpleMessage("حذف الحساب"),
        "deleteAll": MessageLookupByLibrary.simpleMessage("حذف الكل"),
        "deleteAllDescription": MessageLookupByLibrary.simpleMessage(
            "حذف جميع العقارات المحددة نهائياً"),
        "deleteComment": MessageLookupByLibrary.simpleMessage("حذف التعليق"),
        "deleteSelected": MessageLookupByLibrary.simpleMessage("حذف المحدد"),
        "deleteSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من حذف العقارات المحددة؟ لا يمكن التراجع عن هذا الإجراء."),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "descriptionLabel": MessageLookupByLibrary.simpleMessage("الوصف"),
        "details": MessageLookupByLibrary.simpleMessage("التفاصيل"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("جارٍ تحديد المدينة..."),
        "didntReceiveCode":
            MessageLookupByLibrary.simpleMessage("لم تستلم الكود؟"),
        "discountDuration": MessageLookupByLibrary.simpleMessage("مدة الخصم"),
        "discountPercentage":
            MessageLookupByLibrary.simpleMessage("نسبة الخصم"),
        "discoverLatestVisualContent":
            MessageLookupByLibrary.simpleMessage("اكتشف أحدث المحتوى المرئي"),
        "discoverMore": MessageLookupByLibrary.simpleMessage("اكتشف المزيد"),
        "documentSelected":
            MessageLookupByLibrary.simpleMessage("تم اختيار الوثيقة"),
        "done": MessageLookupByLibrary.simpleMessage("تم"),
        "downloadData": MessageLookupByLibrary.simpleMessage("تحميل البيانات"),
        "downloadDataMessage": MessageLookupByLibrary.simpleMessage(
            "سيتم إرسال نسخة من جميع بياناتك إلى بريدك الإلكتروني خلال 24 ساعة."),
        "downloadDataSuccess":
            MessageLookupByLibrary.simpleMessage("تم طلب تحميل البيانات بنجاح"),
        "downloadMyData": MessageLookupByLibrary.simpleMessage("تحميل بياناتي"),
        "downloadMyDataSubtitle": MessageLookupByLibrary.simpleMessage(
            "احصل على نسخة من جميع بياناتك"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("تحميل الإيصال"),
        "draftStatusDescription": MessageLookupByLibrary.simpleMessage(
            "عقارك محفوظ ولكن غير منشور بعد"),
        "drafts": MessageLookupByLibrary.simpleMessage("المسودات"),
        "duplicateAll": MessageLookupByLibrary.simpleMessage("نسخ الكل"),
        "duplicateAllDescription": MessageLookupByLibrary.simpleMessage(
            "إنشاء نسخ من العقارات المحددة"),
        "earlyAccessFeatures":
            MessageLookupByLibrary.simpleMessage("مميزات برنامج الوصول المبكر"),
        "earnings": MessageLookupByLibrary.simpleMessage("الأرباح"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("مخطط الأرباح"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الأرباح"),
        "editComment": MessageLookupByLibrary.simpleMessage("تعديل التعليق"),
        "editListing": MessageLookupByLibrary.simpleMessage("تعديل العقار"),
        "editPersonalInfo":
            MessageLookupByLibrary.simpleMessage("تعديل المعلومات الشخصية"),
        "editProfile":
            MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
        "editProfileSubtitle": MessageLookupByLibrary.simpleMessage(
            "تحديث الاسم والصورة والمعلومات الشخصية"),
        "editProfileTitle":
            MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
        "editProperty": MessageLookupByLibrary.simpleMessage("تعديل العقار"),
        "editWhilePending":
            MessageLookupByLibrary.simpleMessage("تعديل أثناء المراجعة"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "emailAddress":
            MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "emailSupport":
            MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "emailVerification":
            MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "enableAllNotifications": MessageLookupByLibrary.simpleMessage(
            "تفعيل أو إلغاء جميع الإشعارات"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "enableNotificationsInSettings": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل الإشعارات في إعدادات الجهاز"),
        "engagementMetrics":
            MessageLookupByLibrary.simpleMessage("مقاييس التفاعل"),
        "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("أدخل المبلغ"),
        "enterBookingRules":
            MessageLookupByLibrary.simpleMessage("أدخل قواعد الحجز"),
        "enterCancellationRules":
            MessageLookupByLibrary.simpleMessage("أدخل قواعد الإلغاء"),
        "enterChangeReason":
            MessageLookupByLibrary.simpleMessage("أدخل سبب تغيير الحالة..."),
        "enterCurrentPassword": MessageLookupByLibrary.simpleMessage(
            "يرجى إدخال كلمة المرور الحالية"),
        "enterNewPassword": MessageLookupByLibrary.simpleMessage(
            "يرجى إدخال كلمة المرور الجديدة"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("ادخل رقم الجوال"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "enterPropertyDescription":
            MessageLookupByLibrary.simpleMessage("أدخل وصف العقار"),
        "enterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان العقار"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة البحث..."),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("أدخل كود التحقق"),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "errorFetchingInfo":
            MessageLookupByLibrary.simpleMessage("حدث خطأ أثناء جلب المعلومات"),
        "errorLoadingListings":
            MessageLookupByLibrary.simpleMessage("خطأ في تحميل العقارات"),
        "errorLoadingProperty":
            MessageLookupByLibrary.simpleMessage("خطأ في تحميل العقار"),
        "errorMessage": MessageLookupByLibrary.simpleMessage("خطأ"),
        "errorOccurred": MessageLookupByLibrary.simpleMessage("خطأ"),
        "errorPersistsContact": MessageLookupByLibrary.simpleMessage(
            "إذا استمر الخطأ، يرجى التواصل مع الدعم"),
        "eventNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الأحداث"),
        "excellent": MessageLookupByLibrary.simpleMessage("ممتاز"),
        "executeAction": MessageLookupByLibrary.simpleMessage("تنفيذ الإجراء"),
        "exploreAllCategoriesSubtitle":
            MessageLookupByLibrary.simpleMessage("استكشف جميع الفئات المتاحة"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("تصفح الأقسام"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("استكشف العقارات"),
        "exportAccountData":
            MessageLookupByLibrary.simpleMessage("تصدير بيانات الحساب"),
        "exportAll": MessageLookupByLibrary.simpleMessage("تصدير الكل"),
        "exportAllDescription":
            MessageLookupByLibrary.simpleMessage("تصدير بيانات العقارات"),
        "exportData": MessageLookupByLibrary.simpleMessage("تصدير البيانات"),
        "facilities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "facilitiesRequired": MessageLookupByLibrary.simpleMessage(
            "يرجى اختيار مرفق واحد على الأقل"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("فشل إنشاء العنصر"),
        "failedToLoadCancellationPolicies":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل سياسات الإلغاء"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الفئات"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل المرافق"),
        "failedToLoadFriends":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الأصدقاء"),
        "failedToLoadPropertyTypes":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل أنواع العقارات"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الريلز"),
        "failedToLoadRequests":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الطلبات"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الفيديو"),
        "fair": MessageLookupByLibrary.simpleMessage("مقبول"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للعائلات"),
        "faq": MessageLookupByLibrary.simpleMessage("الأسئلة الشائعة"),
        "favoriteCount": MessageLookupByLibrary.simpleMessage("المفضلة"),
        "featureRequiresLogin": MessageLookupByLibrary.simpleMessage(
            "هذه الميزة تتطلب تسجيل الدخول"),
        "featureUnavailable":
            MessageLookupByLibrary.simpleMessage("الميزة غير متاحة"),
        "featuredPlaces": MessageLookupByLibrary.simpleMessage("أماكن مميزة"),
        "female": MessageLookupByLibrary.simpleMessage("أنثي"),
        "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
        "filterReels": MessageLookupByLibrary.simpleMessage("تصفية الريلز"),
        "filterResults": MessageLookupByLibrary.simpleMessage("تصفية النتائج"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("السعر النهائي"),
        "findCoHost":
            MessageLookupByLibrary.simpleMessage("العثور على مضيف مشارك"),
        "flexiblePolicy": MessageLookupByLibrary.simpleMessage("مرنة"),
        "foundHelpful":
            MessageLookupByLibrary.simpleMessage("شخص وجد هذا التقييم مفيداً"),
        "fourPlusStars": MessageLookupByLibrary.simpleMessage("4+ نجوم"),
        "freeParking": MessageLookupByLibrary.simpleMessage("موقف مجاني"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("واي فاي مجاني"),
        "freeWifiArabic": MessageLookupByLibrary.simpleMessage("واي فاي مجاني"),
        "frequentQuestions":
            MessageLookupByLibrary.simpleMessage("الأسئلة الشائعة"),
        "frequentlyAskedQuestions":
            MessageLookupByLibrary.simpleMessage("الأسئلة الشائعة"),
        "friendRemovedError":
            MessageLookupByLibrary.simpleMessage("خطأ في إزالة الصديق"),
        "friendRemovedSuccess":
            MessageLookupByLibrary.simpleMessage("تم إزالة الصديق بنجاح"),
        "friendRequestAcceptedError":
            MessageLookupByLibrary.simpleMessage("خطأ في قبول طلب الصداقة"),
        "friendRequestAcceptedSuccess":
            MessageLookupByLibrary.simpleMessage("تم قبول طلب الصداقة بنجاح"),
        "friendRequestDeclinedError":
            MessageLookupByLibrary.simpleMessage("خطأ في رفض طلب الصداقة"),
        "friendRequestDeclinedSuccess":
            MessageLookupByLibrary.simpleMessage("تم رفض طلب الصداقة بنجاح"),
        "friendRequestSentError":
            MessageLookupByLibrary.simpleMessage("خطأ في إرسال طلب الصداقة"),
        "friendRequestSentSuccess":
            MessageLookupByLibrary.simpleMessage("تم إرسال طلب الصداقة بنجاح"),
        "friendRequestTime":
            MessageLookupByLibrary.simpleMessage("طلب الصداقة"),
        "friends": MessageLookupByLibrary.simpleMessage("الأصدقاء"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
        "garden": MessageLookupByLibrary.simpleMessage("حديقة"),
        "gardenView": MessageLookupByLibrary.simpleMessage("إطلالة الحديقة"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("نقطة تجمع"),
        "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
        "goBack": MessageLookupByLibrary.simpleMessage("العودة"),
        "goToPendingRequests": MessageLookupByLibrary.simpleMessage(
            "يرجى الذهاب إلى تبويب الطلبات لقبول طلب الصداقة"),
        "good": MessageLookupByLibrary.simpleMessage("جيد"),
        "gridView": MessageLookupByLibrary.simpleMessage("عرض الشبكة"),
        "guest": MessageLookupByLibrary.simpleMessage("ضيف"),
        "guestComment": MessageLookupByLibrary.simpleMessage("تعليق الضيف"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("مفضل الضيوف"),
        "guestLimitations":
            MessageLookupByLibrary.simpleMessage("قيود وضع الضيف"),
        "guestLimitationsDetails": MessageLookupByLibrary.simpleMessage(
            "في وضع الضيف، بعض الميزات محدودة"),
        "guestModeInfo":
            MessageLookupByLibrary.simpleMessage("معلومات وضع الضيف"),
        "guestName": MessageLookupByLibrary.simpleMessage("اسم الضيف"),
        "guestRating": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guestReservation": MessageLookupByLibrary.simpleMessage("حجز الضيف"),
        "guestReservationMessage": MessageLookupByLibrary.simpleMessage(
            "يمكنك الحجز كضيف ولكن ستحتاج لتسجيل الدخول لاحقاً"),
        "guestReview": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guestUser": MessageLookupByLibrary.simpleMessage("مستخدم ضيف"),
        "guests": MessageLookupByLibrary.simpleMessage("الضيوف"),
        "guestsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد الضيوف يجب أن يكون بين 1 و 20"),
        "guestsRequired":
            MessageLookupByLibrary.simpleMessage("عدد الضيوف مطلوب"),
        "gym": MessageLookupByLibrary.simpleMessage("صالة رياضية"),
        "heating": MessageLookupByLibrary.simpleMessage("تدفئة"),
        "helpAndSupport":
            MessageLookupByLibrary.simpleMessage("المساعدة والدعم"),
        "helpCenter": MessageLookupByLibrary.simpleMessage("مركز المساعدة"),
        "hideComments": MessageLookupByLibrary.simpleMessage("إخفاء التعليقات"),
        "high": MessageLookupByLibrary.simpleMessage("عالي"),
        "highRated": MessageLookupByLibrary.simpleMessage("عالي التقييم"),
        "highestRated": MessageLookupByLibrary.simpleMessage("الأعلى تقييماً"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "host": MessageLookupByLibrary.simpleMessage("مضيف"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("لوحة المضيف"),
        "hostMode": MessageLookupByLibrary.simpleMessage("وضع المستضيف"),
        "hostModeActivated":
            MessageLookupByLibrary.simpleMessage("تم تفعيل وضع المضيف بنجاح"),
        "hostModeDeactivated": MessageLookupByLibrary.simpleMessage(
            "تم إلغاء تفعيل وضع المضيف بنجاح"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "hostName": MessageLookupByLibrary.simpleMessage("اسم المضيف"),
        "hosting": MessageLookupByLibrary.simpleMessage("الاستضافة"),
        "hostingResources":
            MessageLookupByLibrary.simpleMessage("موارد الاستضافة"),
        "hostingTips": MessageLookupByLibrary.simpleMessage("نصائح الاستضافة"),
        "hours": MessageLookupByLibrary.simpleMessage("ساعة"),
        "hoursAgo": MessageLookupByLibrary.simpleMessage("ساعة"),
        "houseRules": MessageLookupByLibrary.simpleMessage("قواعد المنزل"),
        "howToBecomeHost":
            MessageLookupByLibrary.simpleMessage("كيف أصبح مضيف؟"),
        "howToBecomeHostAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك التبديل إلى وضع المضيف من الملف الشخصي وإضافة عقارك الأول."),
        "howToCancelBooking":
            MessageLookupByLibrary.simpleMessage("كيف يمكنني إلغاء حجز؟"),
        "howToCancelBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك إلغاء الحجز من خلال الذهاب إلى \"حجوزاتي\" واختيار الحجز المراد إلغاؤه."),
        "howToChangeBooking": MessageLookupByLibrary.simpleMessage(
            "كيف يمكنني تغيير معلومات الحجز؟"),
        "howToChangeBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "يمكنك تعديل بعض معلومات الحجز من خلال التواصل مع المضيف أو خدمة العملاء."),
        "howToSearch": MessageLookupByLibrary.simpleMessage("كيفية البحث"),
        "identityVerification":
            MessageLookupByLibrary.simpleMessage("الهوية الشخصية"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
        "images": MessageLookupByLibrary.simpleMessage("صور"),
        "imagesMinimum":
            MessageLookupByLibrary.simpleMessage("يرجى إضافة 3 صور على الأقل"),
        "imagesRequired": MessageLookupByLibrary.simpleMessage(
            "يرجى إضافة صورة واحدة على الأقل"),
        "importantInfo": MessageLookupByLibrary.simpleMessage("معلومات مهمة"),
        "inHosting": MessageLookupByLibrary.simpleMessage("في الاستضافة"),
        "inactive": MessageLookupByLibrary.simpleMessage("غير نشطة"),
        "inactiveListings":
            MessageLookupByLibrary.simpleMessage("العقارات غير النشطة"),
        "inactiveStatusDescription":
            MessageLookupByLibrary.simpleMessage("عقارك مخفي عن الضيوف"),
        "increasePrices": MessageLookupByLibrary.simpleMessage("زيادة الأسعار"),
        "increasePricesDescription":
            MessageLookupByLibrary.simpleMessage("زيادة الأسعار بنسبة مئوية"),
        "insights": MessageLookupByLibrary.simpleMessage("الرؤى والأفكار"),
        "instantBook": MessageLookupByLibrary.simpleMessage("حجز فوري"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("كود غير صحيح"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("تاريخ غير صالح"),
        "invalidPhoneNumber":
            MessageLookupByLibrary.simpleMessage("رقم الجوال غير صحيح"),
        "itemsSelected": MessageLookupByLibrary.simpleMessage("عناصر محددة"),
        "january2023": MessageLookupByLibrary.simpleMessage("يناير 2023"),
        "jeddahSaudi": MessageLookupByLibrary.simpleMessage(
            "جدة، المملكة العربية السعودية"),
        "jeddahSaudiArabia": MessageLookupByLibrary.simpleMessage(
            "جدة، المملكة العربية السعودية"),
        "joinAsHost": MessageLookupByLibrary.simpleMessage("انضم كمضيف"),
        "joinAsHostSubtitle": MessageLookupByLibrary.simpleMessage(
            "من السهل بدء الاستضافة وربح إضافي"),
        "kitchen": MessageLookupByLibrary.simpleMessage("مطبخ"),
        "knowledge": MessageLookupByLibrary.simpleMessage("المعارف"),
        "lakeView": MessageLookupByLibrary.simpleMessage("إطلالة البحيرة"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "languages": MessageLookupByLibrary.simpleMessage("اللغات"),
        "last30Days": MessageLookupByLibrary.simpleMessage("آخر 30 يوم"),
        "last6Months": MessageLookupByLibrary.simpleMessage("آخر 6 أشهر"),
        "last7Days": MessageLookupByLibrary.simpleMessage("آخر 7 أيام"),
        "last90Days": MessageLookupByLibrary.simpleMessage("آخر 90 يوم"),
        "lastLogin": MessageLookupByLibrary.simpleMessage("آخر دخول"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("الشهر الماضي"),
        "lastYear": MessageLookupByLibrary.simpleMessage("السنة الماضية"),
        "latitude": MessageLookupByLibrary.simpleMessage("خط العرض"),
        "laundry": MessageLookupByLibrary.simpleMessage("غسيل"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("اترك تقييم"),
        "legal": MessageLookupByLibrary.simpleMessage("قانوني"),
        "listView": MessageLookupByLibrary.simpleMessage("عرض القائمة"),
        "listingStatus": MessageLookupByLibrary.simpleMessage("حالة العقار"),
        "listingsSelected":
            MessageLookupByLibrary.simpleMessage("عقارات محددة"),
        "listingsWillBeAffected":
            MessageLookupByLibrary.simpleMessage("عقارات ستتأثر"),
        "liveChat": MessageLookupByLibrary.simpleMessage("الدردشة المباشرة"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "loadingCancellationPolicies": MessageLookupByLibrary.simpleMessage(
            "جاري تحميل سياسات الإلغاء..."),
        "loadingCategories":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الفئات..."),
        "loadingData":
            MessageLookupByLibrary.simpleMessage("جاري تحميل البيانات"),
        "loadingFacilities":
            MessageLookupByLibrary.simpleMessage("جاري تحميل المرافق..."),
        "loadingFriends":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الأصدقاء..."),
        "loadingPropertyData":
            MessageLookupByLibrary.simpleMessage("جاري تحميل بيانات العقار..."),
        "loadingPropertyTypes": MessageLookupByLibrary.simpleMessage(
            "جاري تحميل أنواع العقارات..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الريلز..."),
        "loadingRequests":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الطلبات..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationAndAddress":
            MessageLookupByLibrary.simpleMessage("الموقع والعنوان"),
        "locationLabel": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل إذن الموقع لاستخدام التطبيق"),
        "locationRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار موقع"),
        "locationSelected":
            MessageLookupByLibrary.simpleMessage("تم اختيار الموقع"),
        "locationSubtitle":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والموقع"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "loginForBetterExperience": MessageLookupByLibrary.simpleMessage(
            "سجل دخولك للحصول على تجربة أفضل"),
        "loginForFullExperience": MessageLookupByLibrary.simpleMessage(
            "سجل دخولك للحصول على تجربة كاملة"),
        "loginRequired":
            MessageLookupByLibrary.simpleMessage("تسجيل الدخول مطلوب"),
        "loginRequiredForFavorites":
            MessageLookupByLibrary.simpleMessage("تسجيل الدخول مطلوب للمفضلة"),
        "loginRequiredForReservation":
            MessageLookupByLibrary.simpleMessage("تسجيل الدخول مطلوب للحجز"),
        "loginRequiredForReviews": MessageLookupByLibrary.simpleMessage(
            "تسجيل الدخول مطلوب للتقييمات"),
        "loginRequiredForSettings": MessageLookupByLibrary.simpleMessage(
            "يجب تسجيل الدخول للوصول إلى إعدادات الحساب"),
        "loginRequiredMessage": MessageLookupByLibrary.simpleMessage(
            "يجب تسجيل الدخول للوصول إلى هذه الميزة"),
        "loginRequiredToViewProfile": MessageLookupByLibrary.simpleMessage(
            "يجب تسجيل الدخول لعرض الملف الشخصي"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد تسجيل الخروج؟"),
        "logoutSuccessful":
            MessageLookupByLibrary.simpleMessage("تم تسجيل الخروج بنجاح"),
        "longTermBookings": MessageLookupByLibrary.simpleMessage(
            "الحجوزات طويلة المدى (>28 يوم)"),
        "longitude": MessageLookupByLibrary.simpleMessage("خط الطول"),
        "low": MessageLookupByLibrary.simpleMessage("منخفض"),
        "lowestRated": MessageLookupByLibrary.simpleMessage("الأقل تقييماً"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("إقامة فاخرة"),
        "main": MessageLookupByLibrary.simpleMessage("رئيسي"),
        "mainImage": MessageLookupByLibrary.simpleMessage("الصورة الرئيسية"),
        "makeBooking": MessageLookupByLibrary.simpleMessage("إجراء حجز"),
        "male": MessageLookupByLibrary.simpleMessage("ذكر"),
        "manageNotificationSettings":
            MessageLookupByLibrary.simpleMessage("إدارة إعدادات الإشعارات"),
        "manageNotifications":
            MessageLookupByLibrary.simpleMessage("إدارة إعدادات الإشعارات"),
        "managementActions":
            MessageLookupByLibrary.simpleMessage("إجراءات الإدارة"),
        "mapView": MessageLookupByLibrary.simpleMessage("عرض الخريطة"),
        "march2024": MessageLookupByLibrary.simpleMessage("مارس 2024"),
        "marketingEmails":
            MessageLookupByLibrary.simpleMessage("رسائل التسويق"),
        "marketingEmailsSubtitle": MessageLookupByLibrary.simpleMessage(
            "تلقي رسائل بريد إلكتروني حول العروض والأخبار"),
        "marketingNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات التسويقية"),
        "maxGuests": MessageLookupByLibrary.simpleMessage("الحد الأقصى للضيوف"),
        "media": MessageLookupByLibrary.simpleMessage("الوسائط"),
        "medium": MessageLookupByLibrary.simpleMessage("متوسط"),
        "memberSince": MessageLookupByLibrary.simpleMessage("عضو منذ"),
        "memberSinceLabel": MessageLookupByLibrary.simpleMessage("عضو منذ"),
        "menu": MessageLookupByLibrary.simpleMessage("القائمة"),
        "messageFeatureInDevelopment":
            MessageLookupByLibrary.simpleMessage("ميزة الرسائل قيد التطوير"),
        "messageNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الرسائل"),
        "minRating":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للتقييم"),
        "minimumNotice":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للإشعار"),
        "minimumRating":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للتقييم"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للسحب: ر.س 50"),
        "minimumWithdrawAmount":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للسحب: ر.س 50"),
        "minutesAgo": MessageLookupByLibrary.simpleMessage("دقيقة"),
        "moderatePolicy": MessageLookupByLibrary.simpleMessage("معتدلة"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("تعديل الحجز"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("السعر الشهري"),
        "moreActions":
            MessageLookupByLibrary.simpleMessage("المزيد من الإجراءات"),
        "mostCommented": MessageLookupByLibrary.simpleMessage("الأكثر تعليقاً"),
        "mostLiked": MessageLookupByLibrary.simpleMessage("الأكثر إعجاباً"),
        "mountainView": MessageLookupByLibrary.simpleMessage("إطلالة جبلية"),
        "mustLogin":
            MessageLookupByLibrary.simpleMessage("يجب عليك تسجيل الدخول"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتسجيل الدخول لعرض حسابك الشخصي"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("كتم الصوت"),
        "mutualFriends": MessageLookupByLibrary.simpleMessage("أصدقاء مشتركين"),
        "myBookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "myListings": MessageLookupByLibrary.simpleMessage("عقاراتي"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("المعالم القريبة"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
        "needHelp": MessageLookupByLibrary.simpleMessage("تحتاج مساعدة؟"),
        "needsAttention": MessageLookupByLibrary.simpleMessage("تحتاج اهتمام"),
        "netRevenue": MessageLookupByLibrary.simpleMessage("صافي الإيرادات"),
        "newEventsAndUpdates": MessageLookupByLibrary.simpleMessage(
            "إشعارات حول الأحداث الجديدة والتحديثات"),
        "newHost": MessageLookupByLibrary.simpleMessage("مستضيف جديد"),
        "newLabel": MessageLookupByLibrary.simpleMessage("الجديد"),
        "newListing": MessageLookupByLibrary.simpleMessage("إعلان جديد"),
        "newMessagesAndChats": MessageLookupByLibrary.simpleMessage(
            "إشعارات الرسائل الجديدة والمحادثات"),
        "newPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور الجديدة"),
        "newPrice": MessageLookupByLibrary.simpleMessage("السعر الجديد"),
        "newTicket": MessageLookupByLibrary.simpleMessage("تذكرة جديدة"),
        "newest": MessageLookupByLibrary.simpleMessage("الأحدث"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "night": MessageLookupByLibrary.simpleMessage("ليلة"),
        "nights": MessageLookupByLibrary.simpleMessage("ليالي"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("عدد الليالي"),
        "noAboutMeAdded": MessageLookupByLibrary.simpleMessage(
            "لم يتم إضافة نبذة شخصية بعد."),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("لا توجد مرافق مدرجة"),
        "noBioAdded": MessageLookupByLibrary.simpleMessage(
            "لم يتم إضافة نبذة شخصية بعد."),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noBookingsSubtitle":
            MessageLookupByLibrary.simpleMessage("لم تقم بأي حجوزات حتى الآن"),
        "noBookingsYet": MessageLookupByLibrary.simpleMessage("لا توجد حجوزات"),
        "noComments": MessageLookupByLibrary.simpleMessage("لا توجد تعليقات"),
        "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد بيانات متاحة"),
        "noDescription": MessageLookupByLibrary.simpleMessage("بدون وصف"),
        "noDescriptionAvailable":
            MessageLookupByLibrary.simpleMessage("لا يوجد وصف متاح."),
        "noFaqsAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد أسئلة شائعة متاحة"),
        "noFriendsDescription": MessageLookupByLibrary.simpleMessage(
            "ابدأ بإضافة أصدقاء جدد للتواصل معهم"),
        "noFriendsYet":
            MessageLookupByLibrary.simpleMessage("لا يوجد أصدقاء بعد"),
        "noInfoAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد معلومات متاحة"),
        "noInternetConnection":
            MessageLookupByLibrary.simpleMessage("لا يوجد اتصال بالإنترنت"),
        "noListingsDescription": MessageLookupByLibrary.simpleMessage(
            "ابدأ رحلة الاستضافة بإنشاء أول عقار لك. شارك مساحتك مع المسافرين وابدأ في الكسب!"),
        "noListingsYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات بعد"),
        "noPendingRequests":
            MessageLookupByLibrary.simpleMessage("لا توجد طلبات معلقة"),
        "noPendingRequestsDescription": MessageLookupByLibrary.simpleMessage(
            "ستظهر هنا طلبات الصداقة الواردة"),
        "noPhotosAdded":
            MessageLookupByLibrary.simpleMessage("لم يتم إضافة صور بعد"),
        "noPropertiesSubtitle":
            MessageLookupByLibrary.simpleMessage("ابدأ بإضافة عقارك الأول"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noResults": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noResultsFound": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noReviews": MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "لا توجد تقييمات تطابق الفلتر المحدد"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noReviewsYet":
            MessageLookupByLibrary.simpleMessage("لا توجد تقييمات بعد"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noSearchResultsDescription": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي مستخدمين بهذا الاسم"),
        "noSecuritySettingsAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد إعدادات أمان متاحة"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("ممنوع التدخين"),
        "noStatisticsAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد إحصائيات متاحة"),
        "noSupportTickets":
            MessageLookupByLibrary.simpleMessage("لا توجد تذاكر دعم"),
        "noTitle": MessageLookupByLibrary.simpleMessage("بدون عنوان"),
        "noView": MessageLookupByLibrary.simpleMessage("بدون إطلالة"),
        "noneSelected":
            MessageLookupByLibrary.simpleMessage("لم يتم اختيار أي شيء"),
        "normalDays":
            MessageLookupByLibrary.simpleMessage("أيام العمل العادية"),
        "notAdded": MessageLookupByLibrary.simpleMessage("لم يتم الإضافة"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("غير متوفر"),
        "notProvided": MessageLookupByLibrary.simpleMessage("غير مقدم"),
        "notSelected": MessageLookupByLibrary.simpleMessage("غير محدد"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("غير محدد"),
        "notUploaded": MessageLookupByLibrary.simpleMessage("لم يتم الرفع"),
        "notificationPermissionRequired":
            MessageLookupByLibrary.simpleMessage("مطلوب إذن الإشعارات"),
        "notificationSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الإشعارات"),
        "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
        "notificationsSubtitle":
            MessageLookupByLibrary.simpleMessage("إدارة إعدادات الإشعارات"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("عدد الأيام"),
        "numberOfGuests": MessageLookupByLibrary.simpleMessage("عدد الضيوف"),
        "occupancyRate": MessageLookupByLibrary.simpleMessage("معدل الإشغال"),
        "oceanView": MessageLookupByLibrary.simpleMessage("إطلالة المحيط"),
        "ofPreposition": MessageLookupByLibrary.simpleMessage("من"),
        "offersAndMarketing": MessageLookupByLibrary.simpleMessage(
            "إشعارات العروض والأخبار التسويقية"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "oldest": MessageLookupByLibrary.simpleMessage("الأقدم"),
        "onePlusStars": MessageLookupByLibrary.simpleMessage("1+ نجوم"),
        "openSettings": MessageLookupByLibrary.simpleMessage("فتح الإعدادات"),
        "or": MessageLookupByLibrary.simpleMessage("أو"),
        "overview": MessageLookupByLibrary.simpleMessage("نظرة عامة"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("مناسب للحفلات"),
        "passwordChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تغيير كلمة المرور بنجاح"),
        "passwordMinLength": MessageLookupByLibrary.simpleMessage(
            "كلمة المرور يجب أن تكون 8 أحرف على الأقل"),
        "passwordsDoNotMatch":
            MessageLookupByLibrary.simpleMessage("كلمة المرور غير متطابقة"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("إيقاف الفيديو"),
        "paymentAndBilling":
            MessageLookupByLibrary.simpleMessage("الدفع والفواتير"),
        "paypal": MessageLookupByLibrary.simpleMessage("باي بال"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("الأرباح المعلقة"),
        "pendingReservations":
            MessageLookupByLibrary.simpleMessage("حجوزات معلقة"),
        "pendingReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "pendingStatusDescription": MessageLookupByLibrary.simpleMessage(
            "عقارك قيد المراجعة من فريقنا"),
        "perNight": MessageLookupByLibrary.simpleMessage("ر.س / الليلة"),
        "percentage": MessageLookupByLibrary.simpleMessage("النسبة المئوية"),
        "performanceGrade":
            MessageLookupByLibrary.simpleMessage("تقييم الأداء"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "personalInfoTab":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "personalInformation":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "petFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للحيوانات الأليفة"),
        "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
        "phoneNumberHint": MessageLookupByLibrary.simpleMessage("5xxxxxxxx"),
        "phoneNumberLabel": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
        "phoneNumberRequired":
            MessageLookupByLibrary.simpleMessage("رقم الهاتف مطلوب"),
        "phoneSupport": MessageLookupByLibrary.simpleMessage("الهاتف"),
        "phoneVerification": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
        "photos": MessageLookupByLibrary.simpleMessage("الصور"),
        "photosAndVideo":
            MessageLookupByLibrary.simpleMessage("الصور والفيديو"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "playVideo": MessageLookupByLibrary.simpleMessage("تشغيل الفيديو"),
        "pleaseBathrooms":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عدد الحمامات"),
        "pleaseCheckPhoneNumber":
            MessageLookupByLibrary.simpleMessage("الرجاء التحقق من رقم الجوال"),
        "pleaseEnterBedrooms":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عدد غرف النوم"),
        "pleaseEnterDescription":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال الوصف"),
        "pleaseEnterMaxGuests": MessageLookupByLibrary.simpleMessage(
            "يرجى إدخال الحد الأقصى للضيوف"),
        "pleaseEnterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("الرجاء ادخال رقم الجوال"),
        "pleaseEnterPrice":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال السعر"),
        "pleaseEnterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عنوان العقار"),
        "pleaseEnterSubject":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال الموضوع"),
        "pleaseEnterTitle":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عنوان العقار"),
        "pleaseEnterValidNumber":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال رقم صحيح"),
        "pleaseEnterValidPrice":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال سعر صحيح"),
        "pleaseSelectBirthdate":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار تاريخ الميلاد"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار كلا التاريخين"),
        "pleaseSelectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار سياسة الإلغاء"),
        "pleaseSelectCategory":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار الفئة"),
        "pleaseSelectPropertyType":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار نوع العقار"),
        "pleaseWaitProcessing": MessageLookupByLibrary.simpleMessage(
            "يرجى الانتظار أثناء معالجة معلوماتك"),
        "policies": MessageLookupByLibrary.simpleMessage("السياسات"),
        "policyDescription":
            MessageLookupByLibrary.simpleMessage("وصف السياسة"),
        "pool": MessageLookupByLibrary.simpleMessage("مسبح"),
        "poor": MessageLookupByLibrary.simpleMessage("ضعيف"),
        "popular": MessageLookupByLibrary.simpleMessage("الأكثر شهرة"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("الوجهات الشائعة"),
        "popularPlaces":
            MessageLookupByLibrary.simpleMessage("الأماكن الشائعة"),
        "postComment": MessageLookupByLibrary.simpleMessage("نشر التعليق"),
        "preferences": MessageLookupByLibrary.simpleMessage("التفضيلات"),
        "preferredByGuests":
            MessageLookupByLibrary.simpleMessage("مفضّل لدى الضيوف"),
        "previous": MessageLookupByLibrary.simpleMessage("السابق"),
        "previousTrip": MessageLookupByLibrary.simpleMessage("رحلة سابقة"),
        "previousTrips":
            MessageLookupByLibrary.simpleMessage("الرحلات السابقة"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("تفاصيل الأسعار"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("تفاصيل السعر"),
        "priceGuidance": MessageLookupByLibrary.simpleMessage(
            "نصيحة: ابحث عن العقارات المماثلة في منطقتك لتحديد أسعار تنافسية"),
        "priceHighToLow":
            MessageLookupByLibrary.simpleMessage("السعر: من الأعلى للأقل"),
        "priceHint": MessageLookupByLibrary.simpleMessage("100"),
        "priceInvalid":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال سعر صحيح"),
        "priceLowToHigh":
            MessageLookupByLibrary.simpleMessage("السعر: من الأقل للأعلى"),
        "priceMinimum": MessageLookupByLibrary.simpleMessage(
            "الحد الأدنى للسعر 50 ريال سعودي في الليلة"),
        "pricePerNight": MessageLookupByLibrary.simpleMessage("السعر/ليلة"),
        "priceRange": MessageLookupByLibrary.simpleMessage("نطاق السعر"),
        "priceRequired": MessageLookupByLibrary.simpleMessage("السعر مطلوب"),
        "priceType": MessageLookupByLibrary.simpleMessage("نوع السعر"),
        "priceWithCurrency": m0,
        "pricing": MessageLookupByLibrary.simpleMessage("التسعير"),
        "pricingActions":
            MessageLookupByLibrary.simpleMessage("إجراءات التسعير"),
        "priority": MessageLookupByLibrary.simpleMessage("الأولوية"),
        "privacy": MessageLookupByLibrary.simpleMessage("الخصوصية والأمان"),
        "privacyAndSecurity":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والأمان"),
        "privacyLocationSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والموقع"),
        "privacySettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والحماية"),
        "privacySettingsTitle":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية"),
        "proceedAsGuest": MessageLookupByLibrary.simpleMessage("متابعة كضيف"),
        "proceedLabel": MessageLookupByLibrary.simpleMessage("استمرار"),
        "proceedWithApple":
            MessageLookupByLibrary.simpleMessage("المتابعة مع آبل"),
        "proceedWithGoogle":
            MessageLookupByLibrary.simpleMessage("المتابعة مع جوجل"),
        "proceedWithPhone":
            MessageLookupByLibrary.simpleMessage("تابع بأستخدام رقم جوالك"),
        "processing": MessageLookupByLibrary.simpleMessage("قيد المعالجة"),
        "profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "profileImage": MessageLookupByLibrary.simpleMessage("الصورة الشخصية"),
        "profilePrivacy":
            MessageLookupByLibrary.simpleMessage("خصوصية الملف الشخصي"),
        "properties": MessageLookupByLibrary.simpleMessage("العقارات"),
        "propertyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء العقار بنجاح"),
        "propertyDescription": MessageLookupByLibrary.simpleMessage("الوصف"),
        "propertyDescriptionHint":
            MessageLookupByLibrary.simpleMessage("صف عقارك بالتفصيل"),
        "propertyDescriptionRequired":
            MessageLookupByLibrary.simpleMessage("وصف العقار مطلوب"),
        "propertyDescriptionTooShort": MessageLookupByLibrary.simpleMessage(
            "وصف العقار يجب أن يكون 10 أحرف على الأقل"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل العقار"),
        "propertyDetailsDescription": MessageLookupByLibrary.simpleMessage(
            "حدد تفاصيل ووسائل الراحة في عقارك"),
        "propertyImages": MessageLookupByLibrary.simpleMessage("صور العقار"),
        "propertyLocation": MessageLookupByLibrary.simpleMessage("موقع العقار"),
        "propertyName": MessageLookupByLibrary.simpleMessage("اسم العقار"),
        "propertyNotFound":
            MessageLookupByLibrary.simpleMessage("العقار غير موجود"),
        "propertyNotFoundDescription": MessageLookupByLibrary.simpleMessage(
            "العقار الذي تبحث عنه غير موجود أو تم حذفه."),
        "propertyPhotos": MessageLookupByLibrary.simpleMessage("صور العقار"),
        "propertyPreview":
            MessageLookupByLibrary.simpleMessage("معاينة العقار"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("عنوان العقار"),
        "propertyTitleHint":
            MessageLookupByLibrary.simpleMessage("أدخل عنواناً جذاباً لعقارك"),
        "propertyTitleRequired":
            MessageLookupByLibrary.simpleMessage("عنوان العقار مطلوب"),
        "propertyTitleTooShort": MessageLookupByLibrary.simpleMessage(
            "عنوان العقار يجب أن يكون 3 أحرف على الأقل"),
        "propertyType": MessageLookupByLibrary.simpleMessage("نوع العقار"),
        "propertyTypeOption":
            MessageLookupByLibrary.simpleMessage("خيار نوع العقار"),
        "propertyTypeRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار نوع العقار"),
        "propertyUpdatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تحديث العقار بنجاح"),
        "propertyVideoOptional":
            MessageLookupByLibrary.simpleMessage("فيديو العقار (اختياري)"),
        "publishAll": MessageLookupByLibrary.simpleMessage("نشر الكل"),
        "publishAllDescription":
            MessageLookupByLibrary.simpleMessage("نشر جميع العقارات المحددة"),
        "publishListing": MessageLookupByLibrary.simpleMessage("نشر العقار"),
        "publishListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من نشر هذا العقار؟"),
        "publishProperty": MessageLookupByLibrary.simpleMessage("نشر العقار"),
        "pullToRefresh": MessageLookupByLibrary.simpleMessage("اسحب للتحديث"),
        "pushNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات الفورية"),
        "pushNotificationsSubtitle": MessageLookupByLibrary.simpleMessage(
            "تلقي إشعارات فورية للرسائل والحجوزات"),
        "quickHelp": MessageLookupByLibrary.simpleMessage("مساعدة سريعة"),
        "rareFind": MessageLookupByLibrary.simpleMessage("اكتشاف نادر"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "rating0": MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
        "ratingDescription": m1,
        "ratingHighToLow":
            MessageLookupByLibrary.simpleMessage("التقييم: من الأعلى للأقل"),
        "ratingLowToHigh":
            MessageLookupByLibrary.simpleMessage("التقييم: الأقل أولاً"),
        "rebookProperty": MessageLookupByLibrary.simpleMessage("إعادة الحجز"),
        "recentActivities":
            MessageLookupByLibrary.simpleMessage("النشاطات الأخيرة"),
        "recentBookings": MessageLookupByLibrary.simpleMessage("أحدث الحجوزات"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("أحدث التعليقات"),
        "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
        "referHost": MessageLookupByLibrary.simpleMessage("إحالة مضيف"),
        "refreshData": MessageLookupByLibrary.simpleMessage("تحديث البيانات"),
        "refreshFriends":
            MessageLookupByLibrary.simpleMessage("تحديث قائمة الأصدقاء"),
        "refundPercentage":
            MessageLookupByLibrary.simpleMessage("نسبة الاسترداد"),
        "refundPolicy":
            MessageLookupByLibrary.simpleMessage("ما هي سياسة الاسترداد؟"),
        "refundPolicyAnswer": MessageLookupByLibrary.simpleMessage(
            "تختلف سياسة الاسترداد حسب نوع العقار وسياسة المضيف. يمكنك مراجعة التفاصيل في صفحة الحجز."),
        "rejectionReason": MessageLookupByLibrary.simpleMessage("سبب الرفض"),
        "removeFriend": MessageLookupByLibrary.simpleMessage("إزالة صديق"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("إزالة من المفضلة"),
        "replyToComment":
            MessageLookupByLibrary.simpleMessage("رد على التعليق"),
        "requestHelp": MessageLookupByLibrary.simpleMessage("اطلب المساعدة"),
        "requestSent": MessageLookupByLibrary.simpleMessage("تم الإرسال"),
        "requests": MessageLookupByLibrary.simpleMessage("الطلبات"),
        "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الكود"),
        "reservationConfirmed":
            MessageLookupByLibrary.simpleMessage("تم تأكيد الحجز بنجاح!"),
        "reservationFailed":
            MessageLookupByLibrary.simpleMessage("فشل تأكيد الحجز"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز من"),
        "reservationTo":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز إلى"),
        "reservations": MessageLookupByLibrary.simpleMessage("الحجوزات"),
        "reserve": MessageLookupByLibrary.simpleMessage("احجز"),
        "resetFilters": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
        "responseRate": MessageLookupByLibrary.simpleMessage("معدل الاستجابة"),
        "responseTime": MessageLookupByLibrary.simpleMessage("وقت الاستجابة"),
        "restaurant": MessageLookupByLibrary.simpleMessage("مطعم"),
        "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "retryButton": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "retryConnection":
            MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "revenueMetrics":
            MessageLookupByLibrary.simpleMessage("مقاييس الإيرادات"),
        "reviewAndSubmit":
            MessageLookupByLibrary.simpleMessage("المراجعة والإرسال"),
        "reviewDetails":
            MessageLookupByLibrary.simpleMessage("مراجعة التفاصيل"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("مراجعة الحجز"),
        "reviewSubmittedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إرسال التقييم بنجاح"),
        "reviews": MessageLookupByLibrary.simpleMessage("التقييمات"),
        "reviewsCount": MessageLookupByLibrary.simpleMessage("تقييم"),
        "reviewsLoadError":
            MessageLookupByLibrary.simpleMessage("خطأ في تحميل التقييمات"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على التقييمات"),
        "rooms": MessageLookupByLibrary.simpleMessage("غرف"),
        "rules": MessageLookupByLibrary.simpleMessage("القواعد"),
        "safetyFeatures": MessageLookupByLibrary.simpleMessage("ميزات الأمان"),
        "sampleReviewText": MessageLookupByLibrary.simpleMessage(
            "مكان رائع للإقامة! نظيف ومريح وكما هو موصوف تماماً. المضيف كان متجاوباً ومفيداً جداً."),
        "sar": MessageLookupByLibrary.simpleMessage("ر.س"),
        "sarPerNight": MessageLookupByLibrary.simpleMessage("ر.س/ليلة"),
        "saveAsDraft": MessageLookupByLibrary.simpleMessage("حفظ كمسودة"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "saveChangesTooltip":
            MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "savingProperty":
            MessageLookupByLibrary.simpleMessage("جاري حفظ العقار..."),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchError": MessageLookupByLibrary.simpleMessage("خطأ في البحث"),
        "searchForFriends":
            MessageLookupByLibrary.simpleMessage("ابحث عن أصدقاء"),
        "searchForFriendsDescription": MessageLookupByLibrary.simpleMessage(
            "اكتب اسم أو بريد إلكتروني للبحث عن أصدقاء جدد"),
        "searchFriends": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchFriendsHint": MessageLookupByLibrary.simpleMessage(
            "ابحث عن أصدقاء بالاسم أو البريد الإلكتروني"),
        "searchHint":
            MessageLookupByLibrary.simpleMessage("ابحث عن وجهتك المفضلة..."),
        "searchHistoryCleared":
            MessageLookupByLibrary.simpleMessage("تم مسح سجل البحث"),
        "searchListings":
            MessageLookupByLibrary.simpleMessage("البحث في العقارات..."),
        "searchPlaceholder":
            MessageLookupByLibrary.simpleMessage("حياك ... دور علي اللي تبيه"),
        "searchReels": MessageLookupByLibrary.simpleMessage("البحث في الريلز"),
        "searchResults": MessageLookupByLibrary.simpleMessage("نتائج البحث"),
        "searching": MessageLookupByLibrary.simpleMessage("جاري البحث..."),
        "security": MessageLookupByLibrary.simpleMessage("الأمان"),
        "securityTab": MessageLookupByLibrary.simpleMessage("الأمان"),
        "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "selectAction": MessageLookupByLibrary.simpleMessage("اختر الإجراء"),
        "selectActionCategory":
            MessageLookupByLibrary.simpleMessage("اختر فئة الإجراء"),
        "selectAll": MessageLookupByLibrary.simpleMessage("تحديد الكل"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ الميلاد"),
        "selectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("اختر سياسة الإلغاء"),
        "selectCategory": MessageLookupByLibrary.simpleMessage("اختر الفئة"),
        "selectCity": MessageLookupByLibrary.simpleMessage("اختر المدينة"),
        "selectDates": MessageLookupByLibrary.simpleMessage("اختر التواريخ"),
        "selectFacilities":
            MessageLookupByLibrary.simpleMessage("اختيار المرافق"),
        "selectLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "selectMultiple": MessageLookupByLibrary.simpleMessage("تحديد متعدد"),
        "selectNewStatus":
            MessageLookupByLibrary.simpleMessage("اختر الحالة الجديدة"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("اختيار تاريخ الحجز"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("اختر الخدمات المتوفرة"),
        "selected": MessageLookupByLibrary.simpleMessage("محدد"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "الفترة المحددة غير متاحة، يرجى اختيار فترة أخرى."),
        "sendMessage": MessageLookupByLibrary.simpleMessage("إرسال رسالة"),
        "sendTestNotification":
            MessageLookupByLibrary.simpleMessage("إرسال إشعار تجريبي"),
        "serverError":
            MessageLookupByLibrary.simpleMessage("حدث خطأ في الخادم"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "serviceFeeNotRefundable": MessageLookupByLibrary.simpleMessage(
            "رسوم الخدمة غير قابلة للاسترداد"),
        "serviceFeeRefundable":
            MessageLookupByLibrary.simpleMessage("رسوم الخدمة قابلة للاسترداد"),
        "setPrices": MessageLookupByLibrary.simpleMessage("تحديد الأسعار"),
        "setPricesDescription": MessageLookupByLibrary.simpleMessage(
            "تحديد سعر ثابت لجميع العقارات"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
        "shareCount": MessageLookupByLibrary.simpleMessage("المشاركات"),
        "shareLocation": MessageLookupByLibrary.simpleMessage("مشاركة الموقع"),
        "shareLocationSubtitle": MessageLookupByLibrary.simpleMessage(
            "السماح للتطبيق بالوصول إلى موقعك"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("مشاركة العقار"),
        "shortTermBookings": MessageLookupByLibrary.simpleMessage(
            "الحجوزات قصيرة المدى (≤28 يوم)"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("عرض جميع المرافق"),
        "showAllReviews": MessageLookupByLibrary.simpleMessage("عرض جميع"),
        "showComments": MessageLookupByLibrary.simpleMessage("عرض التعليقات"),
        "showEmail":
            MessageLookupByLibrary.simpleMessage("إظهار البريد الإلكتروني"),
        "showEmailSubtitle": MessageLookupByLibrary.simpleMessage(
            "عرض بريدك الإلكتروني في الملف الشخصي"),
        "showLess": MessageLookupByLibrary.simpleMessage("عرض أقل"),
        "showMore": MessageLookupByLibrary.simpleMessage("عرض المزيد"),
        "showPhone": MessageLookupByLibrary.simpleMessage("إظهار رقم الهاتف"),
        "showPhoneSubtitle": MessageLookupByLibrary.simpleMessage(
            "عرض رقم هاتفك في الملف الشخصي"),
        "showProfile":
            MessageLookupByLibrary.simpleMessage("إظهار الملف الشخصي"),
        "showProfileSubtitle": MessageLookupByLibrary.simpleMessage(
            "السماح للآخرين برؤية ملفك الشخصي"),
        "since": MessageLookupByLibrary.simpleMessage("منذ"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "smartEntry": MessageLookupByLibrary.simpleMessage("دخول ذكي"),
        "smokingAllowed": MessageLookupByLibrary.simpleMessage("التدخين مسموح"),
        "sortBy": MessageLookupByLibrary.simpleMessage("ترتيب حسب"),
        "soundClick": MessageLookupByLibrary.simpleMessage("صوت النقرات"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات النقر"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات النقر"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("صوت التمرير"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات التمرير"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات التمرير"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
        "spa": MessageLookupByLibrary.simpleMessage("سبا"),
        "start": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "statisticsTab": MessageLookupByLibrary.simpleMessage("الإحصائيات"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "statusActions": MessageLookupByLibrary.simpleMessage("إجراءات الحالة"),
        "statusChangeError":
            MessageLookupByLibrary.simpleMessage("خطأ في تغيير الحالة"),
        "statusChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تغيير الحالة بنجاح"),
        "streetView": MessageLookupByLibrary.simpleMessage("إطلالة الشارع"),
        "strictPolicy": MessageLookupByLibrary.simpleMessage("مشددة"),
        "subject": MessageLookupByLibrary.simpleMessage("الموضوع"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "submitReview": MessageLookupByLibrary.simpleMessage("إرسال التقييم"),
        "submitTicket": MessageLookupByLibrary.simpleMessage("إرسال التذكرة"),
        "superhost": MessageLookupByLibrary.simpleMessage("مضيف مميز"),
        "supportCenter": MessageLookupByLibrary.simpleMessage("مركز الدعم"),
        "supportTickets": MessageLookupByLibrary.simpleMessage("تذاكر الدعم"),
        "suspendedStatusDescription":
            MessageLookupByLibrary.simpleMessage("تم تعليق عقارك"),
        "switchToTravel":
            MessageLookupByLibrary.simpleMessage("التبديل الي السفر"),
        "switchToTravelMode":
            MessageLookupByLibrary.simpleMessage("التبديل إلى السفر"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("التقاط صورة"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("اضغط لتغيير الصورة"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("اضغط لرفع الصور"),
        "taxes": MessageLookupByLibrary.simpleMessage("الضرائب"),
        "testNotification":
            MessageLookupByLibrary.simpleMessage("إشعار تجريبي"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع الفاتح"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع المظلم"),
        "thirdPartySharing":
            MessageLookupByLibrary.simpleMessage("المشاركة مع الأطراف الثالثة"),
        "thirdPartySharingDesc": MessageLookupByLibrary.simpleMessage(
            "معلومات حول مشاركة البيانات مع الشركاء"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("هذا الشهر"),
        "thisYear": MessageLookupByLibrary.simpleMessage("هذا العام"),
        "threePlusStars": MessageLookupByLibrary.simpleMessage("3+ نجوم"),
        "ticketCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء التذكرة بنجاح"),
        "tip1": MessageLookupByLibrary.simpleMessage(
            "أضف صور عالية الجودة لجذب المزيد من الضيوف"),
        "tip2":
            MessageLookupByLibrary.simpleMessage("اكتب وصفاً مفصلاً لعقارك"),
        "tip3":
            MessageLookupByLibrary.simpleMessage("حدد أسعاراً تنافسية لمنطقتك"),
        "title": MessageLookupByLibrary.simpleMessage("العنوان"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("العنوان والوصف"),
        "topPerforming": MessageLookupByLibrary.simpleMessage("الأفضل أداءً"),
        "topRated": MessageLookupByLibrary.simpleMessage("الأعلى تقييماً"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الإجمالي"),
        "totalBookings":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalBookingsLabel":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("إجمالي الأرباح"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("إجمالي السعر"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("إجمالي العقارات"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalRevenue":
            MessageLookupByLibrary.simpleMessage("إجمالي الإيرادات"),
        "totalReviews":
            MessageLookupByLibrary.simpleMessage("إجمالي التقييمات"),
        "totalReviewsLabel":
            MessageLookupByLibrary.simpleMessage("إجمالي التقييمات"),
        "totalValue": MessageLookupByLibrary.simpleMessage("القيمة الإجمالية"),
        "totalViews": MessageLookupByLibrary.simpleMessage("إجمالي المشاهدات"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("إجمالي المسحوب"),
        "tourismPermitDocument":
            MessageLookupByLibrary.simpleMessage("وثيقة تصريح السياحة *"),
        "tourismPermitDocumentHint": MessageLookupByLibrary.simpleMessage(
            "ارفع وثيقة تصريح السياحة (PDF, DOC, DOCX, JPG, PNG)"),
        "tourismPermitDocumentRequired":
            MessageLookupByLibrary.simpleMessage("وثيقة تصريح السياحة مطلوبة"),
        "tourismPermitDocumentReview":
            MessageLookupByLibrary.simpleMessage("وثيقة تصريح السياحة"),
        "tourismPermitInfo": MessageLookupByLibrary.simpleMessage(
            "تصريح السياحة مطلوب لجميع العقارات. هذا يساعد في بناء الثقة مع الضيوف ويضمن الامتثال للوائح المحلية."),
        "tourismPermitNumber":
            MessageLookupByLibrary.simpleMessage("رقم تصريح السياحة *"),
        "tourismPermitNumberHint": MessageLookupByLibrary.simpleMessage(
            "أدخل رقم تصريح السياحة (مطلوب)"),
        "tourismPermitNumberMinLength": MessageLookupByLibrary.simpleMessage(
            "رقم تصريح السياحة يجب أن يكون 5 أحرف على الأقل"),
        "tourismPermitNumberRequired":
            MessageLookupByLibrary.simpleMessage("رقم تصريح السياحة مطلوب"),
        "tourismPermitNumberReview":
            MessageLookupByLibrary.simpleMessage("رقم تصريح السياحة"),
        "transportation": MessageLookupByLibrary.simpleMessage("المواصلات"),
        "trips": MessageLookupByLibrary.simpleMessage("الرحلات"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("حاول مرة أخرى"),
        "tryDifferentKeywords":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tryDifferentSearch":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tryDifferentSearchCriteria":
            MessageLookupByLibrary.simpleMessage("جرب تعديل معايير البحث"),
        "tutorialVideos":
            MessageLookupByLibrary.simpleMessage("فيديوهات تعليمية"),
        "tv": MessageLookupByLibrary.simpleMessage("تلفزيون"),
        "twoFactorAuth":
            MessageLookupByLibrary.simpleMessage("المصادقة الثنائية"),
        "twoFactorAuthSubtitle":
            MessageLookupByLibrary.simpleMessage("تأمين إضافي لحسابك"),
        "twoPlusStars": MessageLookupByLibrary.simpleMessage("2+ نجوم"),
        "underReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "unexpectedError":
            MessageLookupByLibrary.simpleMessage("حدث خطأ غير متوقع"),
        "uniqueViews":
            MessageLookupByLibrary.simpleMessage("المشاهدات الفريدة"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الوحدة"),
        "unitName": MessageLookupByLibrary.simpleMessage("اسم الوحدة"),
        "unknown": MessageLookupByLibrary.simpleMessage("غير معروف"),
        "unknownError": MessageLookupByLibrary.simpleMessage("خطأ غير معروف"),
        "unknownStatusDescription":
            MessageLookupByLibrary.simpleMessage("حالة غير معروفة"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("إلغاء كتم الصوت"),
        "unverifiedAccount":
            MessageLookupByLibrary.simpleMessage("حساب غير موثق"),
        "updateProperty": MessageLookupByLibrary.simpleMessage("تحديث العقار"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("رفع الوثيقة"),
        "uploaded": MessageLookupByLibrary.simpleMessage("تم الرفع"),
        "urgent": MessageLookupByLibrary.simpleMessage("عاجل"),
        "usefulResources": MessageLookupByLibrary.simpleMessage("مصادر مفيدة"),
        "userGuide": MessageLookupByLibrary.simpleMessage("دليل المستخدم"),
        "validationError":
            MessageLookupByLibrary.simpleMessage("خطأ في التحقق من البيانات"),
        "validationFailed": MessageLookupByLibrary.simpleMessage(
            "يرجى إصلاح الأخطاء والمحاولة مرة أخرى"),
        "verification": MessageLookupByLibrary.simpleMessage("التحقق"),
        "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
            "لقد تم إرسال رمز مكون من 4 أرقام إلى جوالك"),
        "verificationFailed":
            MessageLookupByLibrary.simpleMessage("فشل التحقق"),
        "verified": MessageLookupByLibrary.simpleMessage("موثق"),
        "verifiedAccount": MessageLookupByLibrary.simpleMessage("حساب موثق"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("التحقق من رقم جوالك"),
        "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
        "veryGood": MessageLookupByLibrary.simpleMessage("جيد جداً"),
        "video": MessageLookupByLibrary.simpleMessage("الفيديو"),
        "videoPreview": MessageLookupByLibrary.simpleMessage("معاينة الفيديو"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("عرض جميع الحجوزات"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("عرض جميع التقييمات"),
        "viewAllReviewsWithCount": m2,
        "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
        "viewProfile": MessageLookupByLibrary.simpleMessage("عرض الملف الشخصي"),
        "viewProfileTitle":
            MessageLookupByLibrary.simpleMessage("عرض الملف الشخصي"),
        "viewReservations":
            MessageLookupByLibrary.simpleMessage("عرض الحجوزات"),
        "views": MessageLookupByLibrary.simpleMessage("المشاهدات"),
        "viewsTrend": MessageLookupByLibrary.simpleMessage("اتجاه المشاهدات"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("رصيد المحفظة"),
        "weekendDays":
            MessageLookupByLibrary.simpleMessage("أيام العطلة الأسبوعية"),
        "weekendPrice":
            MessageLookupByLibrary.simpleMessage("سعر عطلة نهاية الأسبوع"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("السعر الأسبوعي"),
        "welcomeGuest": MessageLookupByLibrary.simpleMessage(
            "حيا الله ضيفنا ... متابعة كضيف"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("ماذا يقدم هذا المكان"),
        "whereYoullBe": MessageLookupByLibrary.simpleMessage("أين ستكون"),
        "wifi": MessageLookupByLibrary.simpleMessage("واي فاي"),
        "withdraw": MessageLookupByLibrary.simpleMessage("سحب"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("سحب الأموال"),
        "withdrawalMethod": MessageLookupByLibrary.simpleMessage("طريقة السحب"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("مناسب للعمل"),
        "workspace": MessageLookupByLibrary.simpleMessage("مساحة عمل"),
        "writeComment": MessageLookupByLibrary.simpleMessage("اكتب تعليق..."),
        "year": MessageLookupByLibrary.simpleMessage("سنة"),
        "years": MessageLookupByLibrary.simpleMessage("سنوات"),
        "yearsOnAirbnb": MessageLookupByLibrary.simpleMessage("سنة على Airbnb"),
        "yourRights": MessageLookupByLibrary.simpleMessage("حقوقك"),
        "yourRightsDesc": MessageLookupByLibrary.simpleMessage(
            "حقوق الخصوصية الخاصة بك وكيفية ممارستها")
      };
}
