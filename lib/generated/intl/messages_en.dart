// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m3(distance) => "${distance} km";

  static String m0(price) => "${price} SR";

  static String m1(percent) =>
      "This property is in the top ${percent}% of similar properties, based on reviews and reliability data.";

  static String m2(count) => "Show all ${count} reviews";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutMe": MessageLookupByLibrary.simpleMessage("About Me"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("About this place"),
        "acceptRequest": MessageLookupByLibrary.simpleMessage("Accept"),
        "accessibility": MessageLookupByLibrary.simpleMessage("Accessibility"),
        "accountInfo":
            MessageLookupByLibrary.simpleMessage("Account Information"),
        "accountInfoTitle":
            MessageLookupByLibrary.simpleMessage("Account Information"),
        "accountSettings":
            MessageLookupByLibrary.simpleMessage("Account Settings"),
        "accountSettingsTitle":
            MessageLookupByLibrary.simpleMessage("Account Settings"),
        "accountStatistics":
            MessageLookupByLibrary.simpleMessage("Account Statistics"),
        "acquaintances": MessageLookupByLibrary.simpleMessage("Acquaintances"),
        "actionParameters":
            MessageLookupByLibrary.simpleMessage("Action Parameters"),
        "activate": MessageLookupByLibrary.simpleMessage("Activate"),
        "activateAll": MessageLookupByLibrary.simpleMessage("Activate All"),
        "activateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Make all selected listings visible to guests"),
        "activateSelected":
            MessageLookupByLibrary.simpleMessage("Activate Selected"),
        "activateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to activate the selected listings?"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "activeListings":
            MessageLookupByLibrary.simpleMessage("Active Listings"),
        "activeSessions":
            MessageLookupByLibrary.simpleMessage("Active Sessions"),
        "activeStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is live and visible to guests"),
        "addFriend": MessageLookupByLibrary.simpleMessage("Add"),
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addImages": MessageLookupByLibrary.simpleMessage("Add Images"),
        "addNewListing":
            MessageLookupByLibrary.simpleMessage("Add New Listing"),
        "addPhoto": MessageLookupByLibrary.simpleMessage("Add Photo"),
        "addPhotos": MessageLookupByLibrary.simpleMessage("Add Photos"),
        "addProperty": MessageLookupByLibrary.simpleMessage("Add Property"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("Add to favorites"),
        "addVideo": MessageLookupByLibrary.simpleMessage("Add Video"),
        "added": MessageLookupByLibrary.simpleMessage("Added"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "additionalSettings":
            MessageLookupByLibrary.simpleMessage("Additional Settings"),
        "address": MessageLookupByLibrary.simpleMessage("Address"),
        "adults": MessageLookupByLibrary.simpleMessage("Adults"),
        "advancedBulkActions":
            MessageLookupByLibrary.simpleMessage("Advanced Bulk Actions"),
        "ago": MessageLookupByLibrary.simpleMessage("ago"),
        "agreeToBookingPolicies": MessageLookupByLibrary.simpleMessage(
            "I agree to the booking policies"),
        "airConditioning":
            MessageLookupByLibrary.simpleMessage("Air Conditioning"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allCategories": MessageLookupByLibrary.simpleMessage("All Categories"),
        "allListings": MessageLookupByLibrary.simpleMessage("All Listings"),
        "allReviews": MessageLookupByLibrary.simpleMessage("All"),
        "allowMessages": MessageLookupByLibrary.simpleMessage("Allow Messages"),
        "allowMessagesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Allow hosts and guests to send you messages"),
        "alreadyFriends": MessageLookupByLibrary.simpleMessage("Friends"),
        "amenities": MessageLookupByLibrary.simpleMessage("Amenities"),
        "analytics": MessageLookupByLibrary.simpleMessage("Analytics"),
        "analyticsDataCollection":
            MessageLookupByLibrary.simpleMessage("Analytics Data Collection"),
        "analyticsDataCollectionSubtitle": MessageLookupByLibrary.simpleMessage(
            "Help us improve the app with anonymous data"),
        "analyticsOverview":
            MessageLookupByLibrary.simpleMessage("Analytics Overview"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "Property booking and rental app"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appInformation":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appName": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
        "applyAction": MessageLookupByLibrary.simpleMessage("Apply Action"),
        "applyDiscount": MessageLookupByLibrary.simpleMessage("Apply Discount"),
        "applyDiscountDescription":
            MessageLookupByLibrary.simpleMessage("Apply temporary discount"),
        "applyFilter": MessageLookupByLibrary.simpleMessage("Apply Filter"),
        "applyFilters": MessageLookupByLibrary.simpleMessage("Apply"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "arabicEnglish":
            MessageLookupByLibrary.simpleMessage("Arabic, English"),
        "archiveAll": MessageLookupByLibrary.simpleMessage("Archive All"),
        "archiveAllDescription":
            MessageLookupByLibrary.simpleMessage("Archive selected listings"),
        "available247": MessageLookupByLibrary.simpleMessage("Available 24/7"),
        "available24_7": MessageLookupByLibrary.simpleMessage("Available 24/7"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("Available Balance"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("Available Services"),
        "average": MessageLookupByLibrary.simpleMessage("Average"),
        "averageDailyRate":
            MessageLookupByLibrary.simpleMessage("Average Daily Rate"),
        "averageRating": MessageLookupByLibrary.simpleMessage("Average Rating"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("Back to Search"),
        "balcony": MessageLookupByLibrary.simpleMessage("Balcony"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("Bank Transfer"),
        "bar": MessageLookupByLibrary.simpleMessage("Bar"),
        "basicInformation":
            MessageLookupByLibrary.simpleMessage("Basic Information"),
        "basicInformationDesc":
            MessageLookupByLibrary.simpleMessage("Tell us about your property"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("Bathrooms"),
        "bathsInvalid": MessageLookupByLibrary.simpleMessage(
            "Number of bathrooms must be between 1 and 10"),
        "bathsRequired": MessageLookupByLibrary.simpleMessage(
            "Number of bathrooms is required"),
        "beFirstToReview": MessageLookupByLibrary.simpleMessage(
            "Be the first to review this property"),
        "beFirstToReviewThisPlace": MessageLookupByLibrary.simpleMessage(
            "Be the first to review this place"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("Beach Access"),
        "becomeHost":
            MessageLookupByLibrary.simpleMessage("How do I become a host?"),
        "becomeHostAnswer": MessageLookupByLibrary.simpleMessage(
            "You can switch to host mode from your profile and add your first property."),
        "bedrooms": MessageLookupByLibrary.simpleMessage("Bedrooms"),
        "bedsInvalid": MessageLookupByLibrary.simpleMessage(
            "Number of bedrooms must be between 1 and 10"),
        "bedsRequired": MessageLookupByLibrary.simpleMessage(
            "Number of bedrooms is required"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "birthdate": MessageLookupByLibrary.simpleMessage("Birthdate"),
        "blockedUsers": MessageLookupByLibrary.simpleMessage("Blocked Users"),
        "blockedUsersSubtitle":
            MessageLookupByLibrary.simpleMessage("Manage blocked users list"),
        "bookNow": MessageLookupByLibrary.simpleMessage("Book Now"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("Booking Date"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("Booking Details & Policies"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("Booking fee"),
        "bookingMetrics":
            MessageLookupByLibrary.simpleMessage("Booking Metrics"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("Booking Policy"),
        "bookingRules": MessageLookupByLibrary.simpleMessage("Booking Rules"),
        "bookingRulesDescription": MessageLookupByLibrary.simpleMessage(
            "Set booking rules and provide tourism permit information"),
        "bookingRulesHint": MessageLookupByLibrary.simpleMessage(
            "Enter any specific rules for booking your property (optional)"),
        "bookingRulesMinLength": MessageLookupByLibrary.simpleMessage(
            "Booking rules should be at least 10 characters if provided"),
        "bookingRulesReview":
            MessageLookupByLibrary.simpleMessage("Booking Rules"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("Booking Status"),
        "bookingSummary":
            MessageLookupByLibrary.simpleMessage("Booking Summary"),
        "bookingWindow": MessageLookupByLibrary.simpleMessage("Booking Window"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("Bookings Chart"),
        "bookingsCount": MessageLookupByLibrary.simpleMessage("booking"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("Bookings Overview"),
        "browseReels": MessageLookupByLibrary.simpleMessage("Browse Reels"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("Budget Friendly"),
        "bulkActionCompleted": MessageLookupByLibrary.simpleMessage(
            "Bulk action completed successfully"),
        "bulkActionError": MessageLookupByLibrary.simpleMessage(
            "Error performing bulk action"),
        "bulkActions": MessageLookupByLibrary.simpleMessage("Bulk Actions"),
        "cacheCleared": MessageLookupByLibrary.simpleMessage("Cache cleared"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("Cancel Booking"),
        "cancelBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "You can cancel your booking by going to \"My Bookings\" and selecting the booking you want to cancel."),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("Cancel Reservation"),
        "cancelSelection":
            MessageLookupByLibrary.simpleMessage("Cancel Selection"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Cancellation Policy"),
        "cancellationPolicyNote": MessageLookupByLibrary.simpleMessage(
            "Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy."),
        "cancellationPolicyRequired": MessageLookupByLibrary.simpleMessage(
            "Please select a cancellation policy"),
        "cancellationRate":
            MessageLookupByLibrary.simpleMessage("Cancellation Rate"),
        "cancellationRules":
            MessageLookupByLibrary.simpleMessage("Cancellation Rules"),
        "cancellationWindow":
            MessageLookupByLibrary.simpleMessage("Cancellation Window"),
        "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "cancelledBookings":
            MessageLookupByLibrary.simpleMessage("Cancelled Bookings"),
        "cannotLoadUserData": MessageLookupByLibrary.simpleMessage(
            "Cannot load user data. Please try again."),
        "categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "categoryAndType":
            MessageLookupByLibrary.simpleMessage("Category & Type"),
        "categoryRequired":
            MessageLookupByLibrary.simpleMessage("Please select a category"),
        "categoryTypeDescription": MessageLookupByLibrary.simpleMessage(
            "Choose the category and type that best describes your property"),
        "change": MessageLookupByLibrary.simpleMessage("Change"),
        "changeBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "You can modify some booking information by contacting the host or customer service."),
        "changeBookingInfo": MessageLookupByLibrary.simpleMessage(
            "How can I change booking information?"),
        "changeDocument":
            MessageLookupByLibrary.simpleMessage("Change Document"),
        "changeListingStatus":
            MessageLookupByLibrary.simpleMessage("Change Listing Status"),
        "changeLocation":
            MessageLookupByLibrary.simpleMessage("Change Location"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "changePasswordButton":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "changePasswordSubtitle":
            MessageLookupByLibrary.simpleMessage("Update your password"),
        "changePasswordTitle":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "changeReason": MessageLookupByLibrary.simpleMessage("Change Reason"),
        "changeStatus": MessageLookupByLibrary.simpleMessage("Change Status"),
        "changeVideo": MessageLookupByLibrary.simpleMessage("Change Video"),
        "charts": MessageLookupByLibrary.simpleMessage("Charts"),
        "checkConnection": MessageLookupByLibrary.simpleMessage(
            "Check your internet connection"),
        "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
        "checkInDate": MessageLookupByLibrary.simpleMessage("Check-in Date"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("Check-in Instructions"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("Check-in/out"),
        "checkInternetConnection": MessageLookupByLibrary.simpleMessage(
            "Please check your internet connection and try again"),
        "checkOut": MessageLookupByLibrary.simpleMessage("Check Out"),
        "checkOutDate": MessageLookupByLibrary.simpleMessage("Check-out Date"),
        "children": MessageLookupByLibrary.simpleMessage("Children"),
        "chooseFromGallery":
            MessageLookupByLibrary.simpleMessage("Choose from Gallery"),
        "chooseMultiple":
            MessageLookupByLibrary.simpleMessage("Choose Multiple"),
        "chooseWhatToDoNow":
            MessageLookupByLibrary.simpleMessage("Choose what to do now"),
        "city": MessageLookupByLibrary.simpleMessage("City"),
        "cityView": MessageLookupByLibrary.simpleMessage("City View"),
        "cleaningFeeNotRefundable":
            MessageLookupByLibrary.simpleMessage("Cleaning fee not refundable"),
        "cleaningFeeRefundable":
            MessageLookupByLibrary.simpleMessage("Cleaning fee refundable"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "clearCache": MessageLookupByLibrary.simpleMessage("Clear Cache"),
        "clearCacheMessage": MessageLookupByLibrary.simpleMessage(
            "Do you want to delete all temporary files?"),
        "clearCacheSubtitle": MessageLookupByLibrary.simpleMessage(
            "Delete temporary files and saved images"),
        "clearCacheTitle": MessageLookupByLibrary.simpleMessage("Clear Cache"),
        "clearFilter": MessageLookupByLibrary.simpleMessage("Clear Filter"),
        "clearFilters": MessageLookupByLibrary.simpleMessage("Clear Filters"),
        "clearSearchHistory":
            MessageLookupByLibrary.simpleMessage("Clear Search History"),
        "clearSearchHistoryMessage": MessageLookupByLibrary.simpleMessage(
            "Do you want to delete all previous searches?"),
        "clearSearchHistorySubtitle": MessageLookupByLibrary.simpleMessage(
            "Delete all previous searches"),
        "clearSearchHistoryTitle":
            MessageLookupByLibrary.simpleMessage("Clear Search History"),
        "clearSelection":
            MessageLookupByLibrary.simpleMessage("Clear Selection"),
        "codeExpired": MessageLookupByLibrary.simpleMessage("Code expired"),
        "comment": MessageLookupByLibrary.simpleMessage("Comment"),
        "commentFailed":
            MessageLookupByLibrary.simpleMessage("Failed to post comment"),
        "commentPosted":
            MessageLookupByLibrary.simpleMessage("Comment posted successfully"),
        "comments": MessageLookupByLibrary.simpleMessage("Comments"),
        "commission": MessageLookupByLibrary.simpleMessage("Commission"),
        "communication": MessageLookupByLibrary.simpleMessage("Communication"),
        "completePersonalInfo": MessageLookupByLibrary.simpleMessage(
            "Complete personal information"),
        "completed": MessageLookupByLibrary.simpleMessage("Completed"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirmBooking":
            MessageLookupByLibrary.simpleMessage("Confirm Booking"),
        "confirmLocation":
            MessageLookupByLibrary.simpleMessage("Confirm Location"),
        "confirmNewPassword":
            MessageLookupByLibrary.simpleMessage("Confirm New Password"),
        "confirmNewPasswordField":
            MessageLookupByLibrary.simpleMessage("Please confirm new password"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("Confirm Reservation"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("Confirm Submission"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to submit this property?"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("Confirmed Bookings"),
        "connectedDevices":
            MessageLookupByLibrary.simpleMessage("Connected Devices"),
        "connectedDevicesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Manage devices you\'re logged in from"),
        "connectionError":
            MessageLookupByLibrary.simpleMessage("Connection Error"),
        "contactHost": MessageLookupByLibrary.simpleMessage("Contact Host"),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Contact Information"),
        "contactSupport":
            MessageLookupByLibrary.simpleMessage("Contact Support"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "contactUsDesc": MessageLookupByLibrary.simpleMessage(
            "Get in touch with our support team"),
        "continueAsGuest":
            MessageLookupByLibrary.simpleMessage("Continue as Guest"),
        "continueButton": MessageLookupByLibrary.simpleMessage("Continue"),
        "conversionRate":
            MessageLookupByLibrary.simpleMessage("Conversion Rate"),
        "convertToDraft":
            MessageLookupByLibrary.simpleMessage("Convert to Draft"),
        "convertToDraftDescription": MessageLookupByLibrary.simpleMessage(
            "Convert all selected listings to draft status"),
        "country": MessageLookupByLibrary.simpleMessage("Country"),
        "createFirstListing":
            MessageLookupByLibrary.simpleMessage("Create Your First Listing"),
        "createNewListing":
            MessageLookupByLibrary.simpleMessage("Create New Listing"),
        "createNewProperty":
            MessageLookupByLibrary.simpleMessage("Create New Property"),
        "createNewSupportTicket":
            MessageLookupByLibrary.simpleMessage("Create New Support Ticket"),
        "createProperty":
            MessageLookupByLibrary.simpleMessage("Create Property"),
        "creatingProperty":
            MessageLookupByLibrary.simpleMessage("Creating your property..."),
        "currencyCode": MessageLookupByLibrary.simpleMessage("SAR"),
        "currencySymbol": MessageLookupByLibrary.simpleMessage("SR"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("Current Location"),
        "currentPassword":
            MessageLookupByLibrary.simpleMessage("Current Password"),
        "currentSession": MessageLookupByLibrary.simpleMessage("Current"),
        "currentStatus": MessageLookupByLibrary.simpleMessage("Current Status"),
        "currentStatusBreakdown":
            MessageLookupByLibrary.simpleMessage("Current Status Breakdown"),
        "customizeExperience": MessageLookupByLibrary.simpleMessage(
            "Customize your app experience"),
        "dailyBookings": MessageLookupByLibrary.simpleMessage("Daily Bookings"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("Daily Price"),
        "dailyRevenue": MessageLookupByLibrary.simpleMessage("Daily Revenue"),
        "dailyViews": MessageLookupByLibrary.simpleMessage("Daily Views"),
        "dangerZone": MessageLookupByLibrary.simpleMessage("Danger Zone"),
        "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
        "dashboardOverview":
            MessageLookupByLibrary.simpleMessage("Dashboard Overview"),
        "dataAndLocation":
            MessageLookupByLibrary.simpleMessage("Data & Location"),
        "dataAndPrivacy":
            MessageLookupByLibrary.simpleMessage("Data & Privacy"),
        "dataCollection":
            MessageLookupByLibrary.simpleMessage("Data Collection"),
        "dataCollectionDesc": MessageLookupByLibrary.simpleMessage(
            "How we collect and use your data"),
        "dataExportSuccessMessage": MessageLookupByLibrary.simpleMessage(
            "Your data has been exported successfully"),
        "dataExportedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Data Exported Successfully"),
        "dataLoadError":
            MessageLookupByLibrary.simpleMessage("Error loading data"),
        "dataLoadFailed":
            MessageLookupByLibrary.simpleMessage("Failed to load data"),
        "dataManagement":
            MessageLookupByLibrary.simpleMessage("Data Management"),
        "dataRetention": MessageLookupByLibrary.simpleMessage("Data Retention"),
        "dataRetentionDesc":
            MessageLookupByLibrary.simpleMessage("How long we keep your data"),
        "dates": MessageLookupByLibrary.simpleMessage("Dates"),
        "days": MessageLookupByLibrary.simpleMessage("Days"),
        "daysAgo": MessageLookupByLibrary.simpleMessage("days ago"),
        "deactivate": MessageLookupByLibrary.simpleMessage("Deactivate"),
        "deactivateAll": MessageLookupByLibrary.simpleMessage("Deactivate All"),
        "deactivateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Hide all selected listings from guests"),
        "deactivateListing":
            MessageLookupByLibrary.simpleMessage("Deactivate Listing"),
        "deactivateListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to deactivate this listing?"),
        "deactivateSelected":
            MessageLookupByLibrary.simpleMessage("Deactivate Selected"),
        "deactivateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to deactivate the selected listings?"),
        "declineRequest": MessageLookupByLibrary.simpleMessage("Decline"),
        "decreasePrices":
            MessageLookupByLibrary.simpleMessage("Decrease Prices"),
        "decreasePricesDescription": MessageLookupByLibrary.simpleMessage(
            "Decrease prices by percentage"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
        "deleteAccountConfirmMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete your account? This action cannot be undone and all your data will be lost."),
        "deleteAccountDesc": MessageLookupByLibrary.simpleMessage(
            "Permanently delete your account and data"),
        "deleteAccountSubtitle": MessageLookupByLibrary.simpleMessage(
            "Permanently delete your account - cannot be undone"),
        "deleteAccountTitle":
            MessageLookupByLibrary.simpleMessage("Delete Account"),
        "deleteAll": MessageLookupByLibrary.simpleMessage("Delete All"),
        "deleteAllDescription": MessageLookupByLibrary.simpleMessage(
            "Permanently delete all selected listings"),
        "deleteComment": MessageLookupByLibrary.simpleMessage("Delete Comment"),
        "deleteSelected":
            MessageLookupByLibrary.simpleMessage("Delete Selected"),
        "deleteSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected listings? This action cannot be undone."),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "descriptionLabel": MessageLookupByLibrary.simpleMessage("Description"),
        "details": MessageLookupByLibrary.simpleMessage("Details"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("Detecting location..."),
        "didntReceiveCode":
            MessageLookupByLibrary.simpleMessage("Didn\'t receive the code?"),
        "discountDuration":
            MessageLookupByLibrary.simpleMessage("Discount Duration"),
        "discountPercentage":
            MessageLookupByLibrary.simpleMessage("Discount Percentage"),
        "discoverLatestVisualContent": MessageLookupByLibrary.simpleMessage(
            "Discover Latest Visual Content"),
        "discoverMore": MessageLookupByLibrary.simpleMessage("Discover More"),
        "distanceInKm": m3,
        "documentSelected":
            MessageLookupByLibrary.simpleMessage("Document Selected"),
        "done": MessageLookupByLibrary.simpleMessage("Done"),
        "downloadData": MessageLookupByLibrary.simpleMessage("Download Data"),
        "downloadDataMessage": MessageLookupByLibrary.simpleMessage(
            "A copy of all your data will be sent to your email within 24 hours."),
        "downloadDataSuccess": MessageLookupByLibrary.simpleMessage(
            "Data download request successful"),
        "downloadMyData":
            MessageLookupByLibrary.simpleMessage("Download My Data"),
        "downloadMyDataSubtitle":
            MessageLookupByLibrary.simpleMessage("Get a copy of all your data"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("Download Receipt"),
        "draftStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is saved but not published yet"),
        "drafts": MessageLookupByLibrary.simpleMessage("Drafts"),
        "duplicateAll": MessageLookupByLibrary.simpleMessage("Duplicate All"),
        "duplicateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Create copies of selected listings"),
        "earlyAccessFeatures":
            MessageLookupByLibrary.simpleMessage("Early Access Features"),
        "earnings": MessageLookupByLibrary.simpleMessage("Earnings"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("Earnings Chart"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("Earnings Overview"),
        "editComment": MessageLookupByLibrary.simpleMessage("Edit Comment"),
        "editListing": MessageLookupByLibrary.simpleMessage("Edit Listing"),
        "editPersonalInfo":
            MessageLookupByLibrary.simpleMessage("Edit Personal Information"),
        "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
        "editProfileSubtitle": MessageLookupByLibrary.simpleMessage(
            "Update name, photo and personal information"),
        "editProfileTitle":
            MessageLookupByLibrary.simpleMessage("Edit Profile"),
        "editProperty": MessageLookupByLibrary.simpleMessage("Edit Property"),
        "editWhilePending":
            MessageLookupByLibrary.simpleMessage("Edit While Pending"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailAddress": MessageLookupByLibrary.simpleMessage("Email Address"),
        "emailSupport": MessageLookupByLibrary.simpleMessage("Email Support"),
        "emailVerification": MessageLookupByLibrary.simpleMessage("Email"),
        "enableAllNotifications": MessageLookupByLibrary.simpleMessage(
            "Enable or disable all notifications"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "enableNotificationsInSettings": MessageLookupByLibrary.simpleMessage(
            "Please enable notifications in device settings"),
        "engagementMetrics":
            MessageLookupByLibrary.simpleMessage("Engagement Metrics"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Enter Amount"),
        "enterBookingRules":
            MessageLookupByLibrary.simpleMessage("Enter booking rules"),
        "enterCancellationRules":
            MessageLookupByLibrary.simpleMessage("Enter cancellation rules"),
        "enterChangeReason": MessageLookupByLibrary.simpleMessage(
            "Enter reason for status change..."),
        "enterCurrentPassword": MessageLookupByLibrary.simpleMessage(
            "Please enter current password"),
        "enterNewPassword":
            MessageLookupByLibrary.simpleMessage("Please enter new password"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter price"),
        "enterPropertyDescription":
            MessageLookupByLibrary.simpleMessage("Enter property description"),
        "enterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("Enter property title"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("Enter search term..."),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Enter verification code"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "errorFetchingInfo": MessageLookupByLibrary.simpleMessage(
            "An error occurred while fetching information"),
        "errorLoadingListings":
            MessageLookupByLibrary.simpleMessage("Error Loading Listings"),
        "errorLoadingProperty":
            MessageLookupByLibrary.simpleMessage("Error Loading Property"),
        "errorMessage": MessageLookupByLibrary.simpleMessage("Error"),
        "errorOccurred": MessageLookupByLibrary.simpleMessage("Error"),
        "errorPersistsContact": MessageLookupByLibrary.simpleMessage(
            "If the error persists, please contact support"),
        "eventNotifications":
            MessageLookupByLibrary.simpleMessage("Event Notifications"),
        "excellent": MessageLookupByLibrary.simpleMessage("Excellent"),
        "executeAction": MessageLookupByLibrary.simpleMessage("Execute Action"),
        "exploreAllCategoriesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Explore all available categories"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("Explore Categories"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("Explore Properties"),
        "exportAccountData":
            MessageLookupByLibrary.simpleMessage("Export Account Data"),
        "exportAll": MessageLookupByLibrary.simpleMessage("Export All"),
        "exportAllDescription":
            MessageLookupByLibrary.simpleMessage("Export listing data"),
        "exportData": MessageLookupByLibrary.simpleMessage("Export Data"),
        "facilities": MessageLookupByLibrary.simpleMessage("Facilities"),
        "facilitiesRequired": MessageLookupByLibrary.simpleMessage(
            "Please select at least one facility"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("Failed to create item"),
        "failedToLoadCancellationPolicies":
            MessageLookupByLibrary.simpleMessage(
                "Failed to load cancellation policies"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("Failed to load categories"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("Failed to load facilities"),
        "failedToLoadFriends":
            MessageLookupByLibrary.simpleMessage("Failed to load friends"),
        "failedToLoadPropertyTypes": MessageLookupByLibrary.simpleMessage(
            "Failed to load property types"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("Failed to load reels"),
        "failedToLoadRequests":
            MessageLookupByLibrary.simpleMessage("Failed to load requests"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("Failed to load video"),
        "fair": MessageLookupByLibrary.simpleMessage("Fair"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("Family Friendly"),
        "faq": MessageLookupByLibrary.simpleMessage("FAQ"),
        "favorite": MessageLookupByLibrary.simpleMessage("Favorite"),
        "favoriteCount": MessageLookupByLibrary.simpleMessage("Favorites"),
        "featureRequiresLogin": MessageLookupByLibrary.simpleMessage(
            "This feature requires you to login. Please create an account or login to access this feature."),
        "featureUnavailable":
            MessageLookupByLibrary.simpleMessage("Feature Unavailable"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("Featured Places"),
        "female": MessageLookupByLibrary.simpleMessage("Female"),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "filterReels": MessageLookupByLibrary.simpleMessage("Filter Reels"),
        "filterResults": MessageLookupByLibrary.simpleMessage("Filter Results"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("Final Price"),
        "findCoHost": MessageLookupByLibrary.simpleMessage("Find Co-Host"),
        "flexiblePolicy": MessageLookupByLibrary.simpleMessage("Flexible"),
        "foundHelpful": MessageLookupByLibrary.simpleMessage(
            "people found this review helpful"),
        "fourPlusStars": MessageLookupByLibrary.simpleMessage("4+ stars"),
        "freeParking": MessageLookupByLibrary.simpleMessage("Free Parking"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("Free WiFi"),
        "freeWifiArabic": MessageLookupByLibrary.simpleMessage("Free WiFi"),
        "frequentQuestions":
            MessageLookupByLibrary.simpleMessage("Frequently Asked Questions"),
        "frequentlyAskedQuestions":
            MessageLookupByLibrary.simpleMessage("Frequently Asked Questions"),
        "friendRemovedError":
            MessageLookupByLibrary.simpleMessage("Error removing friend"),
        "friendRemovedSuccess":
            MessageLookupByLibrary.simpleMessage("Friend removed successfully"),
        "friendRequestAcceptedError": MessageLookupByLibrary.simpleMessage(
            "Error accepting friend request"),
        "friendRequestAcceptedSuccess": MessageLookupByLibrary.simpleMessage(
            "Friend request accepted successfully"),
        "friendRequestDeclinedError": MessageLookupByLibrary.simpleMessage(
            "Error declining friend request"),
        "friendRequestDeclinedSuccess": MessageLookupByLibrary.simpleMessage(
            "Friend request declined successfully"),
        "friendRequestSentError": MessageLookupByLibrary.simpleMessage(
            "Error sending friend request"),
        "friendRequestSentSuccess": MessageLookupByLibrary.simpleMessage(
            "Friend request sent successfully"),
        "friendRequestTime":
            MessageLookupByLibrary.simpleMessage("Friend request"),
        "friends": MessageLookupByLibrary.simpleMessage("Friends"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
        "garden": MessageLookupByLibrary.simpleMessage("Garden"),
        "gardenView": MessageLookupByLibrary.simpleMessage("Garden View"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "goBack": MessageLookupByLibrary.simpleMessage("Go Back"),
        "goToPendingRequests": MessageLookupByLibrary.simpleMessage(
            "Please go to the requests tab to accept the friend request"),
        "good": MessageLookupByLibrary.simpleMessage("Good"),
        "gridView": MessageLookupByLibrary.simpleMessage("Grid View"),
        "guest": MessageLookupByLibrary.simpleMessage("Guest"),
        "guestComment": MessageLookupByLibrary.simpleMessage("Guest Comment"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("Guest Favorite"),
        "guestLimitations":
            MessageLookupByLibrary.simpleMessage("Guest Limitations:"),
        "guestLimitationsDetails": MessageLookupByLibrary.simpleMessage(
            "• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management"),
        "guestModeInfo": MessageLookupByLibrary.simpleMessage(
            "As a guest, you can browse and make reservations, but some features like favorites and reviews require an account."),
        "guestName": MessageLookupByLibrary.simpleMessage("Guest Name"),
        "guestRating": MessageLookupByLibrary.simpleMessage("Guest review"),
        "guestReservation":
            MessageLookupByLibrary.simpleMessage("Guest Reservation"),
        "guestReservationMessage": MessageLookupByLibrary.simpleMessage(
            "You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features."),
        "guestReview": MessageLookupByLibrary.simpleMessage("Guest Review"),
        "guestUser": MessageLookupByLibrary.simpleMessage("Guest User"),
        "guests": MessageLookupByLibrary.simpleMessage("Guests"),
        "guestsInvalid": MessageLookupByLibrary.simpleMessage(
            "Number of guests must be between 1 and 20"),
        "guestsRequired": MessageLookupByLibrary.simpleMessage(
            "Number of guests is required"),
        "gym": MessageLookupByLibrary.simpleMessage("Gym"),
        "heating": MessageLookupByLibrary.simpleMessage("Heating"),
        "helpAndSupport":
            MessageLookupByLibrary.simpleMessage("Help & Support"),
        "helpCenter": MessageLookupByLibrary.simpleMessage("Help Center"),
        "hideComments": MessageLookupByLibrary.simpleMessage("Hide Comments"),
        "high": MessageLookupByLibrary.simpleMessage("High"),
        "highRated": MessageLookupByLibrary.simpleMessage("High Rated"),
        "highestRated": MessageLookupByLibrary.simpleMessage("Highest Rated"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "host": MessageLookupByLibrary.simpleMessage("Host"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("Host Dashboard"),
        "hostMode": MessageLookupByLibrary.simpleMessage("Host Mode"),
        "hostModeActivated": MessageLookupByLibrary.simpleMessage(
            "Host mode activated successfully"),
        "hostModeDeactivated": MessageLookupByLibrary.simpleMessage(
            "Host mode deactivated successfully"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "hostName": MessageLookupByLibrary.simpleMessage("Host Name"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("Hosted by"),
        "hosting": MessageLookupByLibrary.simpleMessage("Hosting"),
        "hostingResources":
            MessageLookupByLibrary.simpleMessage("Hosting Resources"),
        "hostingTips": MessageLookupByLibrary.simpleMessage("Hosting Tips"),
        "hours": MessageLookupByLibrary.simpleMessage("hours"),
        "hoursAgo": MessageLookupByLibrary.simpleMessage("hours ago"),
        "houseRules": MessageLookupByLibrary.simpleMessage("House Rules"),
        "howToBecomeHost":
            MessageLookupByLibrary.simpleMessage("How do I become a host?"),
        "howToBecomeHostAnswer": MessageLookupByLibrary.simpleMessage(
            "You can switch to host mode from your profile and add your first property."),
        "howToCancelBooking":
            MessageLookupByLibrary.simpleMessage("How can I cancel a booking?"),
        "howToCancelBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "You can cancel the booking by going to \"My Bookings\" and selecting the booking you want to cancel."),
        "howToChangeBooking": MessageLookupByLibrary.simpleMessage(
            "How can I change booking information?"),
        "howToChangeBookingAnswer": MessageLookupByLibrary.simpleMessage(
            "You can modify some booking information by contacting the host or customer service."),
        "howToSearch": MessageLookupByLibrary.simpleMessage("How to Search"),
        "identityVerification":
            MessageLookupByLibrary.simpleMessage("Identity"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("Image Gallery"),
        "images": MessageLookupByLibrary.simpleMessage("images"),
        "imagesMinimum": MessageLookupByLibrary.simpleMessage(
            "Please add at least 3 images"),
        "imagesRequired": MessageLookupByLibrary.simpleMessage(
            "Please add at least one image"),
        "importantInfo":
            MessageLookupByLibrary.simpleMessage("Important Information"),
        "inHosting": MessageLookupByLibrary.simpleMessage("hosting"),
        "inactive": MessageLookupByLibrary.simpleMessage("Inactive"),
        "inactiveListings":
            MessageLookupByLibrary.simpleMessage("Inactive Listings"),
        "inactiveStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is hidden from guests"),
        "increasePrices":
            MessageLookupByLibrary.simpleMessage("Increase Prices"),
        "increasePricesDescription": MessageLookupByLibrary.simpleMessage(
            "Increase prices by percentage"),
        "insights": MessageLookupByLibrary.simpleMessage("Insights"),
        "instantBook": MessageLookupByLibrary.simpleMessage("Instant Book"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Invalid code"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("Invalid date"),
        "invalidPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Invalid phone number"),
        "itemsSelected": MessageLookupByLibrary.simpleMessage("items selected"),
        "january2023": MessageLookupByLibrary.simpleMessage("January 2023"),
        "jeddahSaudi":
            MessageLookupByLibrary.simpleMessage("Jeddah, Saudi Arabia"),
        "jeddahSaudiArabia":
            MessageLookupByLibrary.simpleMessage("Jeddah, Saudi Arabia"),
        "joinAsHost": MessageLookupByLibrary.simpleMessage("Join as Host"),
        "joinAsHostSubtitle": MessageLookupByLibrary.simpleMessage(
            "It\'s easy to start hosting and earn extra income"),
        "kitchen": MessageLookupByLibrary.simpleMessage("Kitchen"),
        "knowledge": MessageLookupByLibrary.simpleMessage("Knowledge"),
        "lakeView": MessageLookupByLibrary.simpleMessage("Lake View"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "languages": MessageLookupByLibrary.simpleMessage("Languages"),
        "last30Days": MessageLookupByLibrary.simpleMessage("Last 30 Days"),
        "last6Months": MessageLookupByLibrary.simpleMessage("Last 6 Months"),
        "last7Days": MessageLookupByLibrary.simpleMessage("Last 7 Days"),
        "last90Days": MessageLookupByLibrary.simpleMessage("Last 90 Days"),
        "lastLogin": MessageLookupByLibrary.simpleMessage("Last Login"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("Last Month"),
        "lastYear": MessageLookupByLibrary.simpleMessage("Last Year"),
        "latitude": MessageLookupByLibrary.simpleMessage("Latitude"),
        "laundry": MessageLookupByLibrary.simpleMessage("Laundry"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("Leave Review"),
        "legal": MessageLookupByLibrary.simpleMessage("Legal"),
        "listView": MessageLookupByLibrary.simpleMessage("List View"),
        "listingStatus": MessageLookupByLibrary.simpleMessage("Listing Status"),
        "listingsSelected":
            MessageLookupByLibrary.simpleMessage("listings selected"),
        "listingsWillBeAffected":
            MessageLookupByLibrary.simpleMessage("listings will be affected"),
        "liveChat": MessageLookupByLibrary.simpleMessage("Live Chat"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loadingCancellationPolicies": MessageLookupByLibrary.simpleMessage(
            "Loading cancellation policies..."),
        "loadingCategories":
            MessageLookupByLibrary.simpleMessage("Loading categories..."),
        "loadingData": MessageLookupByLibrary.simpleMessage("Loading data..."),
        "loadingFacilities":
            MessageLookupByLibrary.simpleMessage("Loading facilities..."),
        "loadingFriends":
            MessageLookupByLibrary.simpleMessage("Loading friends..."),
        "loadingPropertyData":
            MessageLookupByLibrary.simpleMessage("Loading property data..."),
        "loadingPropertyTypes":
            MessageLookupByLibrary.simpleMessage("Loading property types..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("Loading reels..."),
        "loadingRequests":
            MessageLookupByLibrary.simpleMessage("Loading requests..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationAndAddress":
            MessageLookupByLibrary.simpleMessage("Location & Address"),
        "locationLabel": MessageLookupByLibrary.simpleMessage("Location"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "Please enable location permission to use the app"),
        "locationRequired":
            MessageLookupByLibrary.simpleMessage("Please select a location"),
        "locationSelected":
            MessageLookupByLibrary.simpleMessage("Location Selected"),
        "locationSubtitle": MessageLookupByLibrary.simpleMessage(
            "Privacy and location settings"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "loginForBetterExperience":
            MessageLookupByLibrary.simpleMessage("Login for Better Experience"),
        "loginForFullExperience":
            MessageLookupByLibrary.simpleMessage("Login for full experience"),
        "loginRequired": MessageLookupByLibrary.simpleMessage("Login Required"),
        "loginRequiredForFavorites": MessageLookupByLibrary.simpleMessage(
            "You need to login to add items to your favorites list."),
        "loginRequiredForReservation": MessageLookupByLibrary.simpleMessage(
            "You need to login to make a reservation. Guest users can also make reservations with limited features."),
        "loginRequiredForReviews": MessageLookupByLibrary.simpleMessage(
            "You need to login to write reviews and share your experience."),
        "loginRequiredForSettings": MessageLookupByLibrary.simpleMessage(
            "You must login to access account settings"),
        "loginRequiredMessage": MessageLookupByLibrary.simpleMessage(
            "You must login to access this feature"),
        "loginRequiredToViewProfile": MessageLookupByLibrary.simpleMessage(
            "Login required to view profile"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to logout?"),
        "logoutSuccessful":
            MessageLookupByLibrary.simpleMessage("Logout Successful"),
        "longTermBookings": MessageLookupByLibrary.simpleMessage(
            "Long-term bookings (>28 days)"),
        "longitude": MessageLookupByLibrary.simpleMessage("Longitude"),
        "low": MessageLookupByLibrary.simpleMessage("Low"),
        "lowestRated": MessageLookupByLibrary.simpleMessage("Lowest Rated"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("Luxury Stay"),
        "main": MessageLookupByLibrary.simpleMessage("Main"),
        "mainImage": MessageLookupByLibrary.simpleMessage("Main Image"),
        "makeBooking": MessageLookupByLibrary.simpleMessage("Make a Booking"),
        "male": MessageLookupByLibrary.simpleMessage("Male"),
        "manageNotificationSettings": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "manageNotifications": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "managementActions":
            MessageLookupByLibrary.simpleMessage("Management Actions"),
        "mapView": MessageLookupByLibrary.simpleMessage("Map view"),
        "march2024": MessageLookupByLibrary.simpleMessage("March 2024"),
        "marketingEmails":
            MessageLookupByLibrary.simpleMessage("Marketing Emails"),
        "marketingEmailsSubtitle": MessageLookupByLibrary.simpleMessage(
            "Receive email messages about offers and news"),
        "marketingNotifications":
            MessageLookupByLibrary.simpleMessage("Marketing Notifications"),
        "maxGuests": MessageLookupByLibrary.simpleMessage("Max Guests"),
        "media": MessageLookupByLibrary.simpleMessage("Media"),
        "medium": MessageLookupByLibrary.simpleMessage("Medium"),
        "memberSince": MessageLookupByLibrary.simpleMessage("Member since"),
        "memberSinceLabel":
            MessageLookupByLibrary.simpleMessage("Member Since"),
        "menu": MessageLookupByLibrary.simpleMessage("Menu"),
        "messageFeatureInDevelopment": MessageLookupByLibrary.simpleMessage(
            "Messaging feature is under development"),
        "messageNotifications":
            MessageLookupByLibrary.simpleMessage("Message Notifications"),
        "minRating": MessageLookupByLibrary.simpleMessage("Minimum Rating"),
        "minimumNotice": MessageLookupByLibrary.simpleMessage("Minimum Notice"),
        "minimumRating": MessageLookupByLibrary.simpleMessage("Minimum Rating"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("Minimum withdrawal: SR 50"),
        "minimumWithdrawAmount":
            MessageLookupByLibrary.simpleMessage("Minimum withdrawal: SR 50"),
        "minutesAgo": MessageLookupByLibrary.simpleMessage("minutes ago"),
        "moderatePolicy": MessageLookupByLibrary.simpleMessage("Moderate"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("Modify Reservation"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("Monthly Price"),
        "moreActions": MessageLookupByLibrary.simpleMessage("More Actions"),
        "mostCommented": MessageLookupByLibrary.simpleMessage("Most Commented"),
        "mostLiked": MessageLookupByLibrary.simpleMessage("Most Liked"),
        "mountainView": MessageLookupByLibrary.simpleMessage("Mountain View"),
        "mustAgreeToBookingPolicies": MessageLookupByLibrary.simpleMessage(
            "You must agree to the policies to confirm reservation"),
        "mustLogin": MessageLookupByLibrary.simpleMessage("You must login"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "Please login to view your profile"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("Mute Video"),
        "mutualFriends": MessageLookupByLibrary.simpleMessage("mutual friends"),
        "myBookings": MessageLookupByLibrary.simpleMessage("My Bookings"),
        "myListings": MessageLookupByLibrary.simpleMessage("My Listings"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("Nearby Attractions"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Nearby Places"),
        "needHelp": MessageLookupByLibrary.simpleMessage("Need help?"),
        "needsAttention":
            MessageLookupByLibrary.simpleMessage("Needs Attention"),
        "netRevenue": MessageLookupByLibrary.simpleMessage("Net Revenue"),
        "newEventsAndUpdates": MessageLookupByLibrary.simpleMessage(
            "Notifications about new events and updates"),
        "newHost": MessageLookupByLibrary.simpleMessage("New host"),
        "newLabel": MessageLookupByLibrary.simpleMessage("New"),
        "newListing": MessageLookupByLibrary.simpleMessage("New Listing"),
        "newMessagesAndChats": MessageLookupByLibrary.simpleMessage(
            "Notifications for new messages and conversations"),
        "newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
        "newPrice": MessageLookupByLibrary.simpleMessage("New Price"),
        "newTicket": MessageLookupByLibrary.simpleMessage("New Ticket"),
        "newest": MessageLookupByLibrary.simpleMessage("Newest"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "night": MessageLookupByLibrary.simpleMessage("night"),
        "nights": MessageLookupByLibrary.simpleMessage("Nights"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("Nights Stayed"),
        "noAboutMeAdded":
            MessageLookupByLibrary.simpleMessage("No personal bio added yet."),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("No amenities listed"),
        "noBioAdded": MessageLookupByLibrary.simpleMessage("No bio added yet."),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noBookingsSubtitle": MessageLookupByLibrary.simpleMessage(
            "You haven\'t made any bookings yet"),
        "noBookingsYet":
            MessageLookupByLibrary.simpleMessage("No bookings yet"),
        "noComments": MessageLookupByLibrary.simpleMessage("No comments yet"),
        "noData": MessageLookupByLibrary.simpleMessage("No data available"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("No data available"),
        "noDescription": MessageLookupByLibrary.simpleMessage("No description"),
        "noDescriptionAvailable":
            MessageLookupByLibrary.simpleMessage("No description available."),
        "noFaqsAvailable":
            MessageLookupByLibrary.simpleMessage("No FAQs available"),
        "noFriendsDescription": MessageLookupByLibrary.simpleMessage(
            "Start by adding new friends to connect with them"),
        "noFriendsYet": MessageLookupByLibrary.simpleMessage("No friends yet"),
        "noImage": MessageLookupByLibrary.simpleMessage("No image available"),
        "noInfoAvailable":
            MessageLookupByLibrary.simpleMessage("No information available"),
        "noInternetConnection":
            MessageLookupByLibrary.simpleMessage("No Internet Connection"),
        "noListingsDescription": MessageLookupByLibrary.simpleMessage(
            "Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!"),
        "noListingsYet":
            MessageLookupByLibrary.simpleMessage("No Listings Yet"),
        "noPendingRequests":
            MessageLookupByLibrary.simpleMessage("No pending requests"),
        "noPendingRequestsDescription": MessageLookupByLibrary.simpleMessage(
            "Incoming friend requests will appear here"),
        "noPhotosAdded":
            MessageLookupByLibrary.simpleMessage("No photos added yet"),
        "noPropertiesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Start by adding your first property"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("No properties yet"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noResults": MessageLookupByLibrary.simpleMessage("No results found"),
        "noResultsFound":
            MessageLookupByLibrary.simpleMessage("No results found"),
        "noReviews": MessageLookupByLibrary.simpleMessage("No reviews"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("No reviews found"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "No reviews match the selected filter"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noReviewsYet": MessageLookupByLibrary.simpleMessage("No reviews yet"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("No results found"),
        "noSearchResultsDescription": MessageLookupByLibrary.simpleMessage(
            "No users found with this name"),
        "noSecuritySettingsAvailable": MessageLookupByLibrary.simpleMessage(
            "No security settings available"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("No Smoking"),
        "noStatisticsAvailable":
            MessageLookupByLibrary.simpleMessage("No statistics available"),
        "noSupportTickets":
            MessageLookupByLibrary.simpleMessage("No support tickets"),
        "noTitle": MessageLookupByLibrary.simpleMessage("No title"),
        "noView": MessageLookupByLibrary.simpleMessage("No View"),
        "noneSelected": MessageLookupByLibrary.simpleMessage("None selected"),
        "normalDays": MessageLookupByLibrary.simpleMessage("Normal Days"),
        "notAdded": MessageLookupByLibrary.simpleMessage("Not added"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("Not available"),
        "notProvided": MessageLookupByLibrary.simpleMessage("Not provided"),
        "notSelected": MessageLookupByLibrary.simpleMessage("Not selected"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("Not Specified"),
        "notUploaded": MessageLookupByLibrary.simpleMessage("Not uploaded"),
        "notificationPermissionRequired": MessageLookupByLibrary.simpleMessage(
            "Notification Permission Required"),
        "notificationSettings":
            MessageLookupByLibrary.simpleMessage("Notification Settings"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "notificationsSubtitle": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bathrooms"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bedrooms"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("Number of Days"),
        "numberOfGuests":
            MessageLookupByLibrary.simpleMessage("Number of Guests"),
        "numberOfNights":
            MessageLookupByLibrary.simpleMessage("Number of nights"),
        "occupancyRate": MessageLookupByLibrary.simpleMessage("Occupancy Rate"),
        "oceanView": MessageLookupByLibrary.simpleMessage("Ocean View"),
        "ofPreposition": MessageLookupByLibrary.simpleMessage("of"),
        "offersAndMarketing": MessageLookupByLibrary.simpleMessage(
            "Notifications about offers and marketing news"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldest": MessageLookupByLibrary.simpleMessage("Oldest"),
        "onePlusStars": MessageLookupByLibrary.simpleMessage("1+ stars"),
        "openSettings": MessageLookupByLibrary.simpleMessage("Open Settings"),
        "or": MessageLookupByLibrary.simpleMessage("OR"),
        "overview": MessageLookupByLibrary.simpleMessage("Overview"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("Party Friendly"),
        "passwordChangedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Password changed successfully"),
        "passwordMinLength": MessageLookupByLibrary.simpleMessage(
            "Password must be at least 8 characters"),
        "passwordsDoNotMatch":
            MessageLookupByLibrary.simpleMessage("Passwords do not match"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("Pause Video"),
        "paymentAndBilling":
            MessageLookupByLibrary.simpleMessage("Payment & Billing"),
        "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
        "paypal": MessageLookupByLibrary.simpleMessage("PayPal"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("Pending Earnings"),
        "pendingReservations":
            MessageLookupByLibrary.simpleMessage("Pending Reservations"),
        "pendingReview": MessageLookupByLibrary.simpleMessage("Pending Review"),
        "pendingStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is under review by our team"),
        "perNight": MessageLookupByLibrary.simpleMessage("SR / night"),
        "percentage": MessageLookupByLibrary.simpleMessage("Percentage"),
        "performanceGrade":
            MessageLookupByLibrary.simpleMessage("Performance Grade"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "personalInfoTab":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "personalInformation":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "petFriendly": MessageLookupByLibrary.simpleMessage("Pet Friendly"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "phoneNumberHint": MessageLookupByLibrary.simpleMessage("5xxxxxxxx"),
        "phoneNumberLabel":
            MessageLookupByLibrary.simpleMessage("Phone Number"),
        "phoneNumberRequired":
            MessageLookupByLibrary.simpleMessage("Phone number is required"),
        "phoneSupport": MessageLookupByLibrary.simpleMessage("Phone Support"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("Phone Number"),
        "photos": MessageLookupByLibrary.simpleMessage("Photos"),
        "photosAndVideo":
            MessageLookupByLibrary.simpleMessage("Photos & Video"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
        "playVideo": MessageLookupByLibrary.simpleMessage("Play Video"),
        "pleaseBathrooms":
            MessageLookupByLibrary.simpleMessage("Please enter bathrooms"),
        "pleaseCheckPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Please check phone number"),
        "pleaseEnterBedrooms":
            MessageLookupByLibrary.simpleMessage("Please enter bedrooms"),
        "pleaseEnterDescription":
            MessageLookupByLibrary.simpleMessage("Please enter description"),
        "pleaseEnterMaxGuests":
            MessageLookupByLibrary.simpleMessage("Please enter max guests"),
        "pleaseEnterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Please enter phone number"),
        "pleaseEnterPrice":
            MessageLookupByLibrary.simpleMessage("Please enter price"),
        "pleaseEnterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("Please enter property title"),
        "pleaseEnterSubject":
            MessageLookupByLibrary.simpleMessage("Please enter subject"),
        "pleaseEnterTitle":
            MessageLookupByLibrary.simpleMessage("Please enter property title"),
        "pleaseEnterValidNumber":
            MessageLookupByLibrary.simpleMessage("Please enter valid number"),
        "pleaseEnterValidPrice":
            MessageLookupByLibrary.simpleMessage("Please enter a valid price"),
        "pleaseSelectBirthdate":
            MessageLookupByLibrary.simpleMessage("Please select birthdate"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("Please select both dates"),
        "pleaseSelectCancellationPolicy": MessageLookupByLibrary.simpleMessage(
            "Please select cancellation policy"),
        "pleaseSelectCategory":
            MessageLookupByLibrary.simpleMessage("Please select category"),
        "pleaseSelectPropertyType":
            MessageLookupByLibrary.simpleMessage("Please select property type"),
        "pleaseWaitProcessing": MessageLookupByLibrary.simpleMessage(
            "Please wait while we process your information"),
        "policies": MessageLookupByLibrary.simpleMessage("Policies"),
        "policyDescription":
            MessageLookupByLibrary.simpleMessage("Policy Description"),
        "pool": MessageLookupByLibrary.simpleMessage("Pool"),
        "poor": MessageLookupByLibrary.simpleMessage("Poor"),
        "popular": MessageLookupByLibrary.simpleMessage("Popular"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("Popular Destinations"),
        "popularPlaces": MessageLookupByLibrary.simpleMessage("Popular Places"),
        "postComment": MessageLookupByLibrary.simpleMessage("Post Comment"),
        "preferences": MessageLookupByLibrary.simpleMessage("Preferences"),
        "preferredByGuests":
            MessageLookupByLibrary.simpleMessage("Preferred by guests"),
        "previous": MessageLookupByLibrary.simpleMessage("Previous"),
        "previousTrip": MessageLookupByLibrary.simpleMessage("Previous Trip"),
        "previousTrips": MessageLookupByLibrary.simpleMessage("Previous Trips"),
        "price": MessageLookupByLibrary.simpleMessage("Price"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("Price Breakdown"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("Price Details"),
        "priceGuidance": MessageLookupByLibrary.simpleMessage(
            "Tip: Research similar properties in your area to set competitive pricing"),
        "priceHighToLow":
            MessageLookupByLibrary.simpleMessage("Price: High to Low"),
        "priceHint": MessageLookupByLibrary.simpleMessage("100"),
        "priceInvalid":
            MessageLookupByLibrary.simpleMessage("Please enter a valid price"),
        "priceLowToHigh":
            MessageLookupByLibrary.simpleMessage("Price: Low to High"),
        "priceMinimum": MessageLookupByLibrary.simpleMessage(
            "Minimum price is 50 SAR per night"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("Price per Night (SAR)"),
        "priceRange": MessageLookupByLibrary.simpleMessage("Price Range"),
        "priceRequired":
            MessageLookupByLibrary.simpleMessage("Price is required"),
        "priceType": MessageLookupByLibrary.simpleMessage("Price Type"),
        "priceWithCurrency": m0,
        "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
        "pricingActions":
            MessageLookupByLibrary.simpleMessage("Pricing Actions"),
        "priority": MessageLookupByLibrary.simpleMessage("Priority"),
        "privacy": MessageLookupByLibrary.simpleMessage("Privacy & Security"),
        "privacyAndSecurity": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "privacyLocationSettings": MessageLookupByLibrary.simpleMessage(
            "Privacy and location settings"),
        "privacySettings": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "privacySettingsTitle":
            MessageLookupByLibrary.simpleMessage("Privacy Settings"),
        "proceedAsGuest":
            MessageLookupByLibrary.simpleMessage("Continue as Guest"),
        "proceedLabel": MessageLookupByLibrary.simpleMessage("Continue"),
        "proceedWithApple":
            MessageLookupByLibrary.simpleMessage("Continue with Apple"),
        "proceedWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continue with Google"),
        "proceedWithPhone": MessageLookupByLibrary.simpleMessage(
            "Continue with your phone number"),
        "processing": MessageLookupByLibrary.simpleMessage("Processing"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileImage": MessageLookupByLibrary.simpleMessage("Profile Image"),
        "profilePrivacy":
            MessageLookupByLibrary.simpleMessage("Profile Privacy"),
        "properties": MessageLookupByLibrary.simpleMessage("Properties"),
        "propertyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property created successfully"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("Description"),
        "propertyDescriptionHint": MessageLookupByLibrary.simpleMessage(
            "Describe your property in detail"),
        "propertyDescriptionRequired": MessageLookupByLibrary.simpleMessage(
            "Property description is required"),
        "propertyDescriptionTooShort": MessageLookupByLibrary.simpleMessage(
            "Property description must be at least 10 characters"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("Property Details"),
        "propertyDetailsDescription": MessageLookupByLibrary.simpleMessage(
            "Specify the details and amenities of your property"),
        "propertyImages":
            MessageLookupByLibrary.simpleMessage("Property Images"),
        "propertyLocation":
            MessageLookupByLibrary.simpleMessage("Property Location"),
        "propertyName": MessageLookupByLibrary.simpleMessage("Property Name"),
        "propertyNotFound":
            MessageLookupByLibrary.simpleMessage("Property Not Found"),
        "propertyNotFoundDescription": MessageLookupByLibrary.simpleMessage(
            "The property you\'re looking for doesn\'t exist or has been removed."),
        "propertyPhotos":
            MessageLookupByLibrary.simpleMessage("Property Photos"),
        "propertyPreview":
            MessageLookupByLibrary.simpleMessage("Property Preview"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("Property Title"),
        "propertyTitleHint": MessageLookupByLibrary.simpleMessage(
            "Enter a catchy title for your property"),
        "propertyTitleRequired":
            MessageLookupByLibrary.simpleMessage("Property title is required"),
        "propertyTitleTooShort": MessageLookupByLibrary.simpleMessage(
            "Property title must be at least 3 characters"),
        "propertyType": MessageLookupByLibrary.simpleMessage("Property Type"),
        "propertyTypeOption":
            MessageLookupByLibrary.simpleMessage("Property type option"),
        "propertyTypeRequired": MessageLookupByLibrary.simpleMessage(
            "Please select a property type"),
        "propertyUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property updated successfully"),
        "propertyVideoOptional":
            MessageLookupByLibrary.simpleMessage("Property Video (Optional)"),
        "publishAll": MessageLookupByLibrary.simpleMessage("Publish All"),
        "publishAllDescription": MessageLookupByLibrary.simpleMessage(
            "Publish all selected listings"),
        "publishListing":
            MessageLookupByLibrary.simpleMessage("Publish Listing"),
        "publishListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to publish this listing?"),
        "publishProperty":
            MessageLookupByLibrary.simpleMessage("Publish Property"),
        "pullToRefresh":
            MessageLookupByLibrary.simpleMessage("Pull to refresh"),
        "pushNotifications":
            MessageLookupByLibrary.simpleMessage("Push Notifications"),
        "pushNotificationsSubtitle": MessageLookupByLibrary.simpleMessage(
            "Receive instant notifications for messages and bookings"),
        "quickHelp": MessageLookupByLibrary.simpleMessage("Quick Help"),
        "rareFind": MessageLookupByLibrary.simpleMessage("Rare Find"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "rating0": MessageLookupByLibrary.simpleMessage("0 rating"),
        "ratingDescription": m1,
        "ratingHighToLow":
            MessageLookupByLibrary.simpleMessage("Rating: High to Low"),
        "ratingLowToHigh":
            MessageLookupByLibrary.simpleMessage("Rating: Low to High"),
        "rebookProperty":
            MessageLookupByLibrary.simpleMessage("Rebook Property"),
        "recentActivities":
            MessageLookupByLibrary.simpleMessage("Recent Activities"),
        "recentBookings":
            MessageLookupByLibrary.simpleMessage("Recent Bookings"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("Recent Reviews"),
        "reels": MessageLookupByLibrary.simpleMessage("Reels"),
        "referHost": MessageLookupByLibrary.simpleMessage("Refer Host"),
        "refreshData": MessageLookupByLibrary.simpleMessage("Refresh Data"),
        "refreshFriends":
            MessageLookupByLibrary.simpleMessage("Refresh friends list"),
        "refundPercentage":
            MessageLookupByLibrary.simpleMessage("Refund Percentage"),
        "refundPolicy":
            MessageLookupByLibrary.simpleMessage("What is the refund policy?"),
        "refundPolicyAnswer": MessageLookupByLibrary.simpleMessage(
            "Refund policy varies by property type and host policy. You can review the details on the booking page."),
        "rejectionReason":
            MessageLookupByLibrary.simpleMessage("Rejection Reason"),
        "removeFriend": MessageLookupByLibrary.simpleMessage("Remove friend"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("Remove from Favorites"),
        "replyToComment":
            MessageLookupByLibrary.simpleMessage("Reply to Comment"),
        "requestHelp": MessageLookupByLibrary.simpleMessage("Request Help"),
        "requestSent": MessageLookupByLibrary.simpleMessage("Sent"),
        "requests": MessageLookupByLibrary.simpleMessage("Requests"),
        "resendCode": MessageLookupByLibrary.simpleMessage("Resend code"),
        "reservationConfirmed": MessageLookupByLibrary.simpleMessage(
            "Reservation confirmed successfully!"),
        "reservationFailed": MessageLookupByLibrary.simpleMessage(
            "Failed to confirm reservation"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("Reservation From"),
        "reservationTo": MessageLookupByLibrary.simpleMessage("Reservation To"),
        "reservations": MessageLookupByLibrary.simpleMessage("Reservations"),
        "reserve": MessageLookupByLibrary.simpleMessage("Reserve"),
        "resetFilters": MessageLookupByLibrary.simpleMessage("Reset"),
        "responseRate": MessageLookupByLibrary.simpleMessage("Response Rate"),
        "responseTime": MessageLookupByLibrary.simpleMessage("Response Time"),
        "restaurant": MessageLookupByLibrary.simpleMessage("Restaurant"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "retryButton": MessageLookupByLibrary.simpleMessage("Retry"),
        "retryConnection":
            MessageLookupByLibrary.simpleMessage("Retry Connection"),
        "revenueMetrics":
            MessageLookupByLibrary.simpleMessage("Revenue Metrics"),
        "reviewAndSubmit":
            MessageLookupByLibrary.simpleMessage("Review & Submit"),
        "reviewDetails": MessageLookupByLibrary.simpleMessage("Review Details"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("Review Reservation"),
        "reviewSubmittedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Review submitted successfully"),
        "reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "reviewsCount": MessageLookupByLibrary.simpleMessage("review"),
        "reviewsLoadError":
            MessageLookupByLibrary.simpleMessage("Failed to load reviews"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("Reviews Overview"),
        "rooms": MessageLookupByLibrary.simpleMessage("Rooms"),
        "rules": MessageLookupByLibrary.simpleMessage("Rules"),
        "safetyFeatures":
            MessageLookupByLibrary.simpleMessage("Safety Features"),
        "sampleReviewText": MessageLookupByLibrary.simpleMessage(
            "Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful."),
        "sar": MessageLookupByLibrary.simpleMessage("SR"),
        "sarPerNight": MessageLookupByLibrary.simpleMessage("SR/night"),
        "saveAsDraft": MessageLookupByLibrary.simpleMessage("Save as Draft"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "saveChangesTooltip":
            MessageLookupByLibrary.simpleMessage("Save Changes"),
        "savingProperty":
            MessageLookupByLibrary.simpleMessage("Saving property..."),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchError": MessageLookupByLibrary.simpleMessage("Search error"),
        "searchForFriends":
            MessageLookupByLibrary.simpleMessage("Search for friends"),
        "searchForFriendsDescription": MessageLookupByLibrary.simpleMessage(
            "Type a name or email to search for new friends"),
        "searchFriends": MessageLookupByLibrary.simpleMessage("Search"),
        "searchFriendsHint": MessageLookupByLibrary.simpleMessage(
            "Search for friends by name or email"),
        "searchHint": MessageLookupByLibrary.simpleMessage(
            "Search your favorite destination..."),
        "searchHistoryCleared":
            MessageLookupByLibrary.simpleMessage("Search history cleared"),
        "searchListings":
            MessageLookupByLibrary.simpleMessage("Search listings..."),
        "searchPlaceholder": MessageLookupByLibrary.simpleMessage(
            "Welcome... search for what you want"),
        "searchReels": MessageLookupByLibrary.simpleMessage("Search Reels"),
        "searchResults": MessageLookupByLibrary.simpleMessage("Search Results"),
        "searching": MessageLookupByLibrary.simpleMessage("Searching..."),
        "security": MessageLookupByLibrary.simpleMessage("Security"),
        "securityTab": MessageLookupByLibrary.simpleMessage("Security"),
        "seeAll": MessageLookupByLibrary.simpleMessage("See All"),
        "selectAction": MessageLookupByLibrary.simpleMessage("Select Action"),
        "selectActionCategory":
            MessageLookupByLibrary.simpleMessage("Select Action Category"),
        "selectAll": MessageLookupByLibrary.simpleMessage("Select All"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("Select Birthdate"),
        "selectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Select Cancellation Policy"),
        "selectCategory":
            MessageLookupByLibrary.simpleMessage("Select Category"),
        "selectCity": MessageLookupByLibrary.simpleMessage("Select City"),
        "selectDates": MessageLookupByLibrary.simpleMessage("Select Dates"),
        "selectFacilities":
            MessageLookupByLibrary.simpleMessage("Select Facilities"),
        "selectLocation":
            MessageLookupByLibrary.simpleMessage("Select Location"),
        "selectMultiple":
            MessageLookupByLibrary.simpleMessage("Select Multiple"),
        "selectNewStatus":
            MessageLookupByLibrary.simpleMessage("Select New Status"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("Select Reservation Date"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("Select available services"),
        "selected": MessageLookupByLibrary.simpleMessage("Selected"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "The selected period is not available, please choose another period."),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send message"),
        "sendTestNotification":
            MessageLookupByLibrary.simpleMessage("Send Test Notification"),
        "serverError": MessageLookupByLibrary.simpleMessage("Server Error"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("Service fee"),
        "serviceFeeNotRefundable":
            MessageLookupByLibrary.simpleMessage("Service fee not refundable"),
        "serviceFeeRefundable":
            MessageLookupByLibrary.simpleMessage("Service fee refundable"),
        "setPrices": MessageLookupByLibrary.simpleMessage("Set Prices"),
        "setPricesDescription": MessageLookupByLibrary.simpleMessage(
            "Set fixed price for all listings"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "shareCount": MessageLookupByLibrary.simpleMessage("Shares"),
        "shareLocation": MessageLookupByLibrary.simpleMessage("Share Location"),
        "shareLocationSubtitle": MessageLookupByLibrary.simpleMessage(
            "Allow app to access your location"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("Share Property"),
        "shortTermBookings": MessageLookupByLibrary.simpleMessage(
            "Short-term bookings (≤28 days)"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("Show all amenities"),
        "showAllReviews": MessageLookupByLibrary.simpleMessage("Show all"),
        "showComments": MessageLookupByLibrary.simpleMessage("Show Comments"),
        "showEmail": MessageLookupByLibrary.simpleMessage("Show Email"),
        "showEmailSubtitle": MessageLookupByLibrary.simpleMessage(
            "Display your email in profile"),
        "showLess": MessageLookupByLibrary.simpleMessage("Show less"),
        "showMore": MessageLookupByLibrary.simpleMessage("Show more"),
        "showPhone": MessageLookupByLibrary.simpleMessage("Show Phone Number"),
        "showPhoneSubtitle": MessageLookupByLibrary.simpleMessage(
            "Display your phone number in profile"),
        "showProfile": MessageLookupByLibrary.simpleMessage("Show Profile"),
        "showProfileSubtitle": MessageLookupByLibrary.simpleMessage(
            "Allow others to see your profile"),
        "since": MessageLookupByLibrary.simpleMessage("Since"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "smartEntry": MessageLookupByLibrary.simpleMessage("Smart Entry"),
        "smokingAllowed":
            MessageLookupByLibrary.simpleMessage("Smoking Allowed"),
        "sortBy": MessageLookupByLibrary.simpleMessage("Sort By"),
        "soundClick": MessageLookupByLibrary.simpleMessage("Click Sounds"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("Click sounds disabled"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("Click sounds enabled"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("Scroll Sounds"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds disabled"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds enabled"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("Sound Settings"),
        "spa": MessageLookupByLibrary.simpleMessage("Spa"),
        "start": MessageLookupByLibrary.simpleMessage("Start"),
        "statisticsTab": MessageLookupByLibrary.simpleMessage("Statistics"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "statusActions": MessageLookupByLibrary.simpleMessage("Status Actions"),
        "statusChangeError":
            MessageLookupByLibrary.simpleMessage("Error changing status"),
        "statusChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Status changed successfully"),
        "streetView": MessageLookupByLibrary.simpleMessage("Street View"),
        "strictPolicy": MessageLookupByLibrary.simpleMessage("Strict"),
        "subject": MessageLookupByLibrary.simpleMessage("Subject"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "submitReview": MessageLookupByLibrary.simpleMessage("Submit Review"),
        "submitTicket": MessageLookupByLibrary.simpleMessage("Submit Ticket"),
        "superhost": MessageLookupByLibrary.simpleMessage("Superhost"),
        "supportCenter": MessageLookupByLibrary.simpleMessage("Support Center"),
        "supportTickets":
            MessageLookupByLibrary.simpleMessage("Support Tickets"),
        "suspendedStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing has been suspended"),
        "switchToTravel":
            MessageLookupByLibrary.simpleMessage("Switch to Travel"),
        "switchToTravelMode":
            MessageLookupByLibrary.simpleMessage("Switch to Travel"),
        "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("Tap to change image"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("Tap to upload images"),
        "taxes": MessageLookupByLibrary.simpleMessage("Taxes"),
        "testNotification":
            MessageLookupByLibrary.simpleMessage("Test Notification"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("Light theme enabled"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("Dark theme enabled"),
        "thirdPartySharing":
            MessageLookupByLibrary.simpleMessage("Third Party Sharing"),
        "thirdPartySharingDesc": MessageLookupByLibrary.simpleMessage(
            "Information about data sharing with partners"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("This Month"),
        "thisYear": MessageLookupByLibrary.simpleMessage("This Year"),
        "threePlusStars": MessageLookupByLibrary.simpleMessage("3+ stars"),
        "ticketCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Ticket created successfully"),
        "tip1": MessageLookupByLibrary.simpleMessage(
            "Add high-quality photos to attract more guests"),
        "tip2": MessageLookupByLibrary.simpleMessage(
            "Write a detailed description of your property"),
        "tip3": MessageLookupByLibrary.simpleMessage(
            "Set competitive pricing for your area"),
        "title": MessageLookupByLibrary.simpleMessage("Title"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("Title & Description"),
        "topPerforming": MessageLookupByLibrary.simpleMessage("Top Performing"),
        "topRated": MessageLookupByLibrary.simpleMessage("Top Rated"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
        "totalBookings": MessageLookupByLibrary.simpleMessage("Total Bookings"),
        "totalBookingsLabel":
            MessageLookupByLibrary.simpleMessage("Total Bookings"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("Total Earnings"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("Total Properties"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("Total Reservations"),
        "totalRevenue": MessageLookupByLibrary.simpleMessage("Total Revenue"),
        "totalReviews": MessageLookupByLibrary.simpleMessage("Total Reviews"),
        "totalReviewsLabel":
            MessageLookupByLibrary.simpleMessage("Total Reviews"),
        "totalValue": MessageLookupByLibrary.simpleMessage("Total Value"),
        "totalViews": MessageLookupByLibrary.simpleMessage("Total Views"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("Total Withdrawn"),
        "tourismPermitDocument":
            MessageLookupByLibrary.simpleMessage("Tourism Permit Document *"),
        "tourismPermitDocumentHint": MessageLookupByLibrary.simpleMessage(
            "Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)"),
        "tourismPermitDocumentRequired": MessageLookupByLibrary.simpleMessage(
            "Tourism permit document is required"),
        "tourismPermitDocumentReview":
            MessageLookupByLibrary.simpleMessage("Tourism Permit Document"),
        "tourismPermitInfo": MessageLookupByLibrary.simpleMessage(
            "Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations."),
        "tourismPermitNumber":
            MessageLookupByLibrary.simpleMessage("Tourism Permit Number *"),
        "tourismPermitNumberHint": MessageLookupByLibrary.simpleMessage(
            "Enter your tourism permit number (required)"),
        "tourismPermitNumberMinLength": MessageLookupByLibrary.simpleMessage(
            "Tourism permit number should be at least 5 characters"),
        "tourismPermitNumberRequired": MessageLookupByLibrary.simpleMessage(
            "Tourism permit number is required"),
        "tourismPermitNumberReview":
            MessageLookupByLibrary.simpleMessage("Tourism Permit Number"),
        "transportation":
            MessageLookupByLibrary.simpleMessage("Transportation"),
        "trips": MessageLookupByLibrary.simpleMessage("Trips"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Try Again"),
        "tryDifferentKeywords": MessageLookupByLibrary.simpleMessage(
            "Try searching with different keywords"),
        "tryDifferentSearch": MessageLookupByLibrary.simpleMessage(
            "Try searching with different words"),
        "tryDifferentSearchCriteria": MessageLookupByLibrary.simpleMessage(
            "Try adjusting your search criteria"),
        "tutorialVideos":
            MessageLookupByLibrary.simpleMessage("Tutorial Videos"),
        "tv": MessageLookupByLibrary.simpleMessage("TV"),
        "twoFactorAuth":
            MessageLookupByLibrary.simpleMessage("Two-Factor Authentication"),
        "twoFactorAuthSubtitle": MessageLookupByLibrary.simpleMessage(
            "Additional security for your account"),
        "twoPlusStars": MessageLookupByLibrary.simpleMessage("2+ stars"),
        "underReview": MessageLookupByLibrary.simpleMessage("Under Review"),
        "unexpectedError": MessageLookupByLibrary.simpleMessage(
            "An unexpected error occurred"),
        "uniqueViews": MessageLookupByLibrary.simpleMessage("Unique Views"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("Unit Details"),
        "unitName": MessageLookupByLibrary.simpleMessage("Unit Name"),
        "unknown": MessageLookupByLibrary.simpleMessage("Unknown"),
        "unknownError": MessageLookupByLibrary.simpleMessage("Unknown Error"),
        "unknownStatusDescription":
            MessageLookupByLibrary.simpleMessage("Unknown status"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("Unmute Video"),
        "unverifiedAccount":
            MessageLookupByLibrary.simpleMessage("Unverified Account"),
        "updateProperty":
            MessageLookupByLibrary.simpleMessage("Update Property"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Upload Document"),
        "uploaded": MessageLookupByLibrary.simpleMessage("Uploaded"),
        "urgent": MessageLookupByLibrary.simpleMessage("Urgent"),
        "usefulResources":
            MessageLookupByLibrary.simpleMessage("Useful Resources"),
        "userGuide": MessageLookupByLibrary.simpleMessage("User Guide"),
        "validationError":
            MessageLookupByLibrary.simpleMessage("Validation error"),
        "validationFailed": MessageLookupByLibrary.simpleMessage(
            "Please fix the errors and try again"),
        "verification": MessageLookupByLibrary.simpleMessage("Verification"),
        "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
            "A 4-digit code has been sent to your phone"),
        "verificationFailed":
            MessageLookupByLibrary.simpleMessage("Verification failed"),
        "verified": MessageLookupByLibrary.simpleMessage("Verified"),
        "verifiedAccount":
            MessageLookupByLibrary.simpleMessage("Verified Account"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verify your phone number"),
        "version": MessageLookupByLibrary.simpleMessage("Version"),
        "veryGood": MessageLookupByLibrary.simpleMessage("Very Good"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoPreview": MessageLookupByLibrary.simpleMessage("Video Preview"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("View All Bookings"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("View All Reviews"),
        "viewAllReviewsWithCount": m2,
        "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
        "viewProfile": MessageLookupByLibrary.simpleMessage("View Profile"),
        "viewProfileTitle":
            MessageLookupByLibrary.simpleMessage("View Profile"),
        "viewReservations":
            MessageLookupByLibrary.simpleMessage("View Reservations"),
        "views": MessageLookupByLibrary.simpleMessage("Views"),
        "viewsTrend": MessageLookupByLibrary.simpleMessage("Views Trend"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("Wallet Balance"),
        "weekendDays": MessageLookupByLibrary.simpleMessage("Weekend Days"),
        "weekendPrice": MessageLookupByLibrary.simpleMessage("Weekend Price"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("Weekly Price"),
        "welcomeGuest": MessageLookupByLibrary.simpleMessage(
            "Welcome our guest... continue"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("What this place offers"),
        "whereYoullBe":
            MessageLookupByLibrary.simpleMessage("Where you\'ll be"),
        "wifi": MessageLookupByLibrary.simpleMessage("Wi-Fi"),
        "withdraw": MessageLookupByLibrary.simpleMessage("Withdraw"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("Withdraw Funds"),
        "withdrawalMethod":
            MessageLookupByLibrary.simpleMessage("Withdrawal Method"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("Work Friendly"),
        "workspace": MessageLookupByLibrary.simpleMessage("Workspace"),
        "writeComment":
            MessageLookupByLibrary.simpleMessage("Write a comment..."),
        "year": MessageLookupByLibrary.simpleMessage("year"),
        "years": MessageLookupByLibrary.simpleMessage("years"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("years hosting"),
        "yearsOnAirbnb":
            MessageLookupByLibrary.simpleMessage("Years on Airbnb"),
        "yourRights": MessageLookupByLibrary.simpleMessage("Your Rights"),
        "yourRightsDesc": MessageLookupByLibrary.simpleMessage(
            "Your privacy rights and how to exercise them")
      };
}
