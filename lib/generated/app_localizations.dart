import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S of(BuildContext context) {
    return Localizations.of<S>(context, S)!;
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// No description provided for @preferredByGuests.
  ///
  /// In en, this message translates to:
  /// **'Preferred by guests'**
  String get preferredByGuests;

  /// No description provided for @ratingDescription.
  ///
  /// In en, this message translates to:
  /// **'This property is in the top {percent}% of similar properties, based on reviews and reliability data.'**
  String ratingDescription(Object percent);

  /// No description provided for @viewAllReviewsWithCount.
  ///
  /// In en, this message translates to:
  /// **'Show all {count} reviews'**
  String viewAllReviewsWithCount(Object count);

  /// No description provided for @noReviewsYet.
  ///
  /// In en, this message translates to:
  /// **'No reviews yet'**
  String get noReviewsYet;

  /// No description provided for @beFirstToReviewThisPlace.
  ///
  /// In en, this message translates to:
  /// **'Be the first to review this place'**
  String get beFirstToReviewThisPlace;

  /// No description provided for @aboutThisPlace.
  ///
  /// In en, this message translates to:
  /// **'About this place'**
  String get aboutThisPlace;

  /// No description provided for @showMore.
  ///
  /// In en, this message translates to:
  /// **'Show more'**
  String get showMore;

  /// No description provided for @showLess.
  ///
  /// In en, this message translates to:
  /// **'Show less'**
  String get showLess;

  /// No description provided for @whatThisPlaceOffers.
  ///
  /// In en, this message translates to:
  /// **'What this place offers'**
  String get whatThisPlaceOffers;

  /// No description provided for @showAllAmenities.
  ///
  /// In en, this message translates to:
  /// **'Show all amenities'**
  String get showAllAmenities;

  /// No description provided for @whereYoullBe.
  ///
  /// In en, this message translates to:
  /// **'Where you\'ll be'**
  String get whereYoullBe;

  /// No description provided for @cancellationPolicy.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Policy'**
  String get cancellationPolicy;

  /// No description provided for @cancellationRules.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Rules'**
  String get cancellationRules;

  /// No description provided for @superhost.
  ///
  /// In en, this message translates to:
  /// **'Superhost'**
  String get superhost;

  /// No description provided for @since.
  ///
  /// In en, this message translates to:
  /// **'Since'**
  String get since;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get change;

  /// No description provided for @night.
  ///
  /// In en, this message translates to:
  /// **'night'**
  String get night;

  /// No description provided for @checkIn.
  ///
  /// In en, this message translates to:
  /// **'Check In'**
  String get checkIn;

  /// No description provided for @checkOut.
  ///
  /// In en, this message translates to:
  /// **'Check Out'**
  String get checkOut;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @selectDates.
  ///
  /// In en, this message translates to:
  /// **'Select Dates'**
  String get selectDates;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @soundClick.
  ///
  /// In en, this message translates to:
  /// **'Click Sounds'**
  String get soundClick;

  /// No description provided for @soundScroll.
  ///
  /// In en, this message translates to:
  /// **'Scroll Sounds'**
  String get soundScroll;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @searchHint.
  ///
  /// In en, this message translates to:
  /// **'Search your favorite destination...'**
  String get searchHint;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @noResults.
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResults;

  /// No description provided for @perNight.
  ///
  /// In en, this message translates to:
  /// **'SR / night'**
  String get perNight;

  /// No description provided for @wifi.
  ///
  /// In en, this message translates to:
  /// **'Wi-Fi'**
  String get wifi;

  /// No description provided for @addToFavorites.
  ///
  /// In en, this message translates to:
  /// **'Add to favorites'**
  String get addToFavorites;

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Gather Point'**
  String get appName;

  /// No description provided for @selectCity.
  ///
  /// In en, this message translates to:
  /// **'Select City'**
  String get selectCity;

  /// No description provided for @detectingLocation.
  ///
  /// In en, this message translates to:
  /// **'Detecting location...'**
  String get detectingLocation;

  /// No description provided for @locationPermissionError.
  ///
  /// In en, this message translates to:
  /// **'Please enable location permission to use the app'**
  String get locationPermissionError;

  /// No description provided for @browseReels.
  ///
  /// In en, this message translates to:
  /// **'Browse Reels'**
  String get browseReels;

  /// No description provided for @discoverLatestVisualContent.
  ///
  /// In en, this message translates to:
  /// **'Discover Latest Visual Content'**
  String get discoverLatestVisualContent;

  /// No description provided for @exploreCategories.
  ///
  /// In en, this message translates to:
  /// **'Explore Categories'**
  String get exploreCategories;

  /// No description provided for @nearbyPlaces.
  ///
  /// In en, this message translates to:
  /// **'Nearby Places'**
  String get nearbyPlaces;

  /// No description provided for @popularDestinations.
  ///
  /// In en, this message translates to:
  /// **'Popular Destinations'**
  String get popularDestinations;

  /// No description provided for @featuredPlaces.
  ///
  /// In en, this message translates to:
  /// **'Featured Places'**
  String get featuredPlaces;

  /// No description provided for @discoverMore.
  ///
  /// In en, this message translates to:
  /// **'Discover More'**
  String get discoverMore;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @bookNow.
  ///
  /// In en, this message translates to:
  /// **'Book Now'**
  String get bookNow;

  /// No description provided for @pricePerNight.
  ///
  /// In en, this message translates to:
  /// **'Price per Night (SAR)'**
  String get pricePerNight;

  /// No description provided for @guests.
  ///
  /// In en, this message translates to:
  /// **'Guests'**
  String get guests;

  /// No description provided for @rooms.
  ///
  /// In en, this message translates to:
  /// **'Rooms'**
  String get rooms;

  /// No description provided for @bathrooms.
  ///
  /// In en, this message translates to:
  /// **'Bathrooms'**
  String get bathrooms;

  /// No description provided for @amenities.
  ///
  /// In en, this message translates to:
  /// **'Amenities'**
  String get amenities;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @reviews.
  ///
  /// In en, this message translates to:
  /// **'Reviews'**
  String get reviews;

  /// No description provided for @rating.
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// No description provided for @excellent.
  ///
  /// In en, this message translates to:
  /// **'Excellent'**
  String get excellent;

  /// No description provided for @veryGood.
  ///
  /// In en, this message translates to:
  /// **'Very Good'**
  String get veryGood;

  /// No description provided for @good.
  ///
  /// In en, this message translates to:
  /// **'Good'**
  String get good;

  /// No description provided for @fair.
  ///
  /// In en, this message translates to:
  /// **'Fair'**
  String get fair;

  /// No description provided for @poor.
  ///
  /// In en, this message translates to:
  /// **'Poor'**
  String get poor;

  /// No description provided for @soundSettings.
  ///
  /// In en, this message translates to:
  /// **'Sound Settings'**
  String get soundSettings;

  /// No description provided for @customizeExperience.
  ///
  /// In en, this message translates to:
  /// **'Customize your app experience'**
  String get customizeExperience;

  /// No description provided for @themeEnabled.
  ///
  /// In en, this message translates to:
  /// **'Dark theme enabled'**
  String get themeEnabled;

  /// No description provided for @themeDisabled.
  ///
  /// In en, this message translates to:
  /// **'Light theme enabled'**
  String get themeDisabled;

  /// No description provided for @soundClickEnabled.
  ///
  /// In en, this message translates to:
  /// **'Click sounds enabled'**
  String get soundClickEnabled;

  /// No description provided for @soundClickDisabled.
  ///
  /// In en, this message translates to:
  /// **'Click sounds disabled'**
  String get soundClickDisabled;

  /// No description provided for @soundScrollEnabled.
  ///
  /// In en, this message translates to:
  /// **'Scroll sounds enabled'**
  String get soundScrollEnabled;

  /// No description provided for @soundScrollDisabled.
  ///
  /// In en, this message translates to:
  /// **'Scroll sounds disabled'**
  String get soundScrollDisabled;

  /// No description provided for @createProperty.
  ///
  /// In en, this message translates to:
  /// **'Create Property'**
  String get createProperty;

  /// No description provided for @propertyTitle.
  ///
  /// In en, this message translates to:
  /// **'Property Title'**
  String get propertyTitle;

  /// No description provided for @propertyDescription.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get propertyDescription;

  /// No description provided for @selectCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get selectCategory;

  /// No description provided for @titleAndDescription.
  ///
  /// In en, this message translates to:
  /// **'Title & Description'**
  String get titleAndDescription;

  /// No description provided for @pickLocation.
  ///
  /// In en, this message translates to:
  /// **'Pick Location'**
  String get pickLocation;

  /// No description provided for @imageGallery.
  ///
  /// In en, this message translates to:
  /// **'Image Gallery'**
  String get imageGallery;

  /// No description provided for @addImage.
  ///
  /// In en, this message translates to:
  /// **'Add Image'**
  String get addImage;

  /// No description provided for @availableServices.
  ///
  /// In en, this message translates to:
  /// **'Available Services'**
  String get availableServices;

  /// No description provided for @selectServices.
  ///
  /// In en, this message translates to:
  /// **'Select available services'**
  String get selectServices;

  /// No description provided for @pricing.
  ///
  /// In en, this message translates to:
  /// **'Pricing'**
  String get pricing;

  /// No description provided for @dailyPrice.
  ///
  /// In en, this message translates to:
  /// **'Daily Price'**
  String get dailyPrice;

  /// No description provided for @weeklyPrice.
  ///
  /// In en, this message translates to:
  /// **'Weekly Price'**
  String get weeklyPrice;

  /// No description provided for @monthlyPrice.
  ///
  /// In en, this message translates to:
  /// **'Monthly Price'**
  String get monthlyPrice;

  /// No description provided for @commission.
  ///
  /// In en, this message translates to:
  /// **'Commission'**
  String get commission;

  /// No description provided for @bookingDetails.
  ///
  /// In en, this message translates to:
  /// **'Booking Details & Policies'**
  String get bookingDetails;

  /// No description provided for @numberOfBathrooms.
  ///
  /// In en, this message translates to:
  /// **'Number of Bathrooms'**
  String get numberOfBathrooms;

  /// No description provided for @numberOfBedrooms.
  ///
  /// In en, this message translates to:
  /// **'Number of Bedrooms'**
  String get numberOfBedrooms;

  /// No description provided for @numberOfGuests.
  ///
  /// In en, this message translates to:
  /// **'Number of Guests'**
  String get numberOfGuests;

  /// No description provided for @bookingPolicy.
  ///
  /// In en, this message translates to:
  /// **'Booking Policy'**
  String get bookingPolicy;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @confirmSubmission.
  ///
  /// In en, this message translates to:
  /// **'Confirm Submission'**
  String get confirmSubmission;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @distanceInKm.
  ///
  /// In en, this message translates to:
  /// **'{distance} km'**
  String distanceInKm(Object distance);

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @favorite.
  ///
  /// In en, this message translates to:
  /// **'Favorite'**
  String get favorite;

  /// No description provided for @removeFromFavorites.
  ///
  /// In en, this message translates to:
  /// **'Remove from Favorites'**
  String get removeFromFavorites;

  /// No description provided for @noImage.
  ///
  /// In en, this message translates to:
  /// **'No image available'**
  String get noImage;

  /// No description provided for @confirmSubmissionMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to submit this property?'**
  String get confirmSubmissionMessage;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @propertyCreatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Property created successfully'**
  String get propertyCreatedSuccessfully;

  /// No description provided for @tapToUploadImages.
  ///
  /// In en, this message translates to:
  /// **'Tap to upload images'**
  String get tapToUploadImages;

  /// No description provided for @latitude.
  ///
  /// In en, this message translates to:
  /// **'Latitude'**
  String get latitude;

  /// No description provided for @longitude.
  ///
  /// In en, this message translates to:
  /// **'Longitude'**
  String get longitude;

  /// No description provided for @enterPrice.
  ///
  /// In en, this message translates to:
  /// **'Enter price'**
  String get enterPrice;

  /// No description provided for @failedToLoadCategories.
  ///
  /// In en, this message translates to:
  /// **'Failed to load categories'**
  String get failedToLoadCategories;

  /// No description provided for @failedToLoadFacilities.
  ///
  /// In en, this message translates to:
  /// **'Failed to load facilities'**
  String get failedToLoadFacilities;

  /// No description provided for @failedToCreateItem.
  ///
  /// In en, this message translates to:
  /// **'Failed to create item'**
  String get failedToCreateItem;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @reels.
  ///
  /// In en, this message translates to:
  /// **'Reels'**
  String get reels;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @myBookings.
  ///
  /// In en, this message translates to:
  /// **'My Bookings'**
  String get myBookings;

  /// No description provided for @myListings.
  ///
  /// In en, this message translates to:
  /// **'My Listings'**
  String get myListings;

  /// No description provided for @editProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// No description provided for @personalInfo.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInfo;

  /// No description provided for @contactInfo.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactInfo;

  /// No description provided for @additionalInfo.
  ///
  /// In en, this message translates to:
  /// **'Additional Information'**
  String get additionalInfo;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @bio.
  ///
  /// In en, this message translates to:
  /// **'Bio'**
  String get bio;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @gender.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get gender;

  /// No description provided for @birthdate.
  ///
  /// In en, this message translates to:
  /// **'Birthdate'**
  String get birthdate;

  /// No description provided for @male.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get male;

  /// No description provided for @female.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get female;

  /// No description provided for @notSpecified.
  ///
  /// In en, this message translates to:
  /// **'Not Specified'**
  String get notSpecified;

  /// No description provided for @selectBirthdate.
  ///
  /// In en, this message translates to:
  /// **'Select Birthdate'**
  String get selectBirthdate;

  /// No description provided for @saveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// No description provided for @profileImage.
  ///
  /// In en, this message translates to:
  /// **'Profile Image'**
  String get profileImage;

  /// No description provided for @tapToChangeImage.
  ///
  /// In en, this message translates to:
  /// **'Tap to change image'**
  String get tapToChangeImage;

  /// No description provided for @totalBookings.
  ///
  /// In en, this message translates to:
  /// **'Total Bookings'**
  String get totalBookings;

  /// No description provided for @confirmedBookings.
  ///
  /// In en, this message translates to:
  /// **'Confirmed Bookings'**
  String get confirmedBookings;

  /// No description provided for @totalProperties.
  ///
  /// In en, this message translates to:
  /// **'Total Properties'**
  String get totalProperties;

  /// No description provided for @totalViews.
  ///
  /// In en, this message translates to:
  /// **'Total Views'**
  String get totalViews;

  /// No description provided for @totalReservations.
  ///
  /// In en, this message translates to:
  /// **'Total Reservations'**
  String get totalReservations;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @confirmed.
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @inactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// No description provided for @underReview.
  ///
  /// In en, this message translates to:
  /// **'Under Review'**
  String get underReview;

  /// No description provided for @totalPrice.
  ///
  /// In en, this message translates to:
  /// **'Total Price'**
  String get totalPrice;

  /// No description provided for @viewDetails.
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get viewDetails;

  /// No description provided for @cancelBooking.
  ///
  /// In en, this message translates to:
  /// **'Cancel Booking'**
  String get cancelBooking;

  /// No description provided for @rebookProperty.
  ///
  /// In en, this message translates to:
  /// **'Rebook Property'**
  String get rebookProperty;

  /// No description provided for @editProperty.
  ///
  /// In en, this message translates to:
  /// **'Edit Property'**
  String get editProperty;

  /// No description provided for @bedrooms.
  ///
  /// In en, this message translates to:
  /// **'Bedrooms'**
  String get bedrooms;

  /// No description provided for @propertyDetails.
  ///
  /// In en, this message translates to:
  /// **'Property Details'**
  String get propertyDetails;

  /// No description provided for @hostMode.
  ///
  /// In en, this message translates to:
  /// **'Host Mode'**
  String get hostMode;

  /// No description provided for @enableHostMode.
  ///
  /// In en, this message translates to:
  /// **'Enable host mode to manage your properties'**
  String get enableHostMode;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @appearance.
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get appearance;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @privacy.
  ///
  /// In en, this message translates to:
  /// **'Privacy & Security'**
  String get privacy;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get about;

  /// No description provided for @manageNotifications.
  ///
  /// In en, this message translates to:
  /// **'Manage notification settings'**
  String get manageNotifications;

  /// No description provided for @privacySettings.
  ///
  /// In en, this message translates to:
  /// **'Privacy and security settings'**
  String get privacySettings;

  /// No description provided for @appInfo.
  ///
  /// In en, this message translates to:
  /// **'App information and version'**
  String get appInfo;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @appDescription.
  ///
  /// In en, this message translates to:
  /// **'Property booking and rental app'**
  String get appDescription;

  /// No description provided for @noBookingsYet.
  ///
  /// In en, this message translates to:
  /// **'No bookings yet'**
  String get noBookingsYet;

  /// No description provided for @noBookingsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t made any bookings yet'**
  String get noBookingsSubtitle;

  /// No description provided for @exploreProperties.
  ///
  /// In en, this message translates to:
  /// **'Explore Properties'**
  String get exploreProperties;

  /// No description provided for @noPropertiesYet.
  ///
  /// In en, this message translates to:
  /// **'No properties yet'**
  String get noPropertiesYet;

  /// No description provided for @noPropertiesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Start by adding your first property'**
  String get noPropertiesSubtitle;

  /// No description provided for @addProperty.
  ///
  /// In en, this message translates to:
  /// **'Add Property'**
  String get addProperty;

  /// No description provided for @searching.
  ///
  /// In en, this message translates to:
  /// **'Searching...'**
  String get searching;

  /// No description provided for @tryDifferentKeywords.
  ///
  /// In en, this message translates to:
  /// **'Try searching with different keywords'**
  String get tryDifferentKeywords;

  /// No description provided for @backToSearch.
  ///
  /// In en, this message translates to:
  /// **'Back to Search'**
  String get backToSearch;

  /// No description provided for @loadingReels.
  ///
  /// In en, this message translates to:
  /// **'Loading reels...'**
  String get loadingReels;

  /// No description provided for @failedToLoadReels.
  ///
  /// In en, this message translates to:
  /// **'Failed to load reels'**
  String get failedToLoadReels;

  /// No description provided for @checkConnection.
  ///
  /// In en, this message translates to:
  /// **'Check your internet connection'**
  String get checkConnection;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @shareProperty.
  ///
  /// In en, this message translates to:
  /// **'Share Property'**
  String get shareProperty;

  /// No description provided for @yearsHosting.
  ///
  /// In en, this message translates to:
  /// **'years hosting'**
  String get yearsHosting;

  /// No description provided for @noAmenitiesListed.
  ///
  /// In en, this message translates to:
  /// **'No amenities listed'**
  String get noAmenitiesListed;

  /// No description provided for @mapView.
  ///
  /// In en, this message translates to:
  /// **'Map view'**
  String get mapView;

  /// No description provided for @guestReview.
  ///
  /// In en, this message translates to:
  /// **'Guest Review'**
  String get guestReview;

  /// No description provided for @reserve.
  ///
  /// In en, this message translates to:
  /// **'Reserve'**
  String get reserve;

  /// No description provided for @bookingFee.
  ///
  /// In en, this message translates to:
  /// **'Booking fee'**
  String get bookingFee;

  /// No description provided for @serviceFee.
  ///
  /// In en, this message translates to:
  /// **'Service fee'**
  String get serviceFee;

  /// No description provided for @taxes.
  ///
  /// In en, this message translates to:
  /// **'Taxes'**
  String get taxes;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @currentLocation.
  ///
  /// In en, this message translates to:
  /// **'Current Location'**
  String get currentLocation;

  /// No description provided for @searchPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Welcome... search for what you want'**
  String get searchPlaceholder;

  /// No description provided for @categories.
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// No description provided for @popularPlaces.
  ///
  /// In en, this message translates to:
  /// **'Popular Places'**
  String get popularPlaces;

  /// No description provided for @seeAll.
  ///
  /// In en, this message translates to:
  /// **'See All'**
  String get seeAll;

  /// No description provided for @views.
  ///
  /// In en, this message translates to:
  /// **'Views'**
  String get views;

  /// No description provided for @properties.
  ///
  /// In en, this message translates to:
  /// **'Properties'**
  String get properties;

  /// No description provided for @reservations.
  ///
  /// In en, this message translates to:
  /// **'Reservations'**
  String get reservations;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @checkInDate.
  ///
  /// In en, this message translates to:
  /// **'Check-in Date'**
  String get checkInDate;

  /// No description provided for @checkOutDate.
  ///
  /// In en, this message translates to:
  /// **'Check-out Date'**
  String get checkOutDate;

  /// No description provided for @propertyName.
  ///
  /// In en, this message translates to:
  /// **'Property Name'**
  String get propertyName;

  /// No description provided for @propertyLocation.
  ///
  /// In en, this message translates to:
  /// **'Property Location'**
  String get propertyLocation;

  /// No description provided for @totalAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get totalAmount;

  /// No description provided for @bookingStatus.
  ///
  /// In en, this message translates to:
  /// **'Booking Status'**
  String get bookingStatus;

  /// No description provided for @bookingDate.
  ///
  /// In en, this message translates to:
  /// **'Booking Date'**
  String get bookingDate;

  /// No description provided for @hostName.
  ///
  /// In en, this message translates to:
  /// **'Host Name'**
  String get hostName;

  /// No description provided for @contactHost.
  ///
  /// In en, this message translates to:
  /// **'Contact Host'**
  String get contactHost;

  /// No description provided for @cancelReservation.
  ///
  /// In en, this message translates to:
  /// **'Cancel Reservation'**
  String get cancelReservation;

  /// No description provided for @modifyReservation.
  ///
  /// In en, this message translates to:
  /// **'Modify Reservation'**
  String get modifyReservation;

  /// No description provided for @leaveReview.
  ///
  /// In en, this message translates to:
  /// **'Leave Review'**
  String get leaveReview;

  /// No description provided for @downloadReceipt.
  ///
  /// In en, this message translates to:
  /// **'Download Receipt'**
  String get downloadReceipt;

  /// No description provided for @propertyType.
  ///
  /// In en, this message translates to:
  /// **'Property Type'**
  String get propertyType;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @rules.
  ///
  /// In en, this message translates to:
  /// **'Rules'**
  String get rules;

  /// No description provided for @safetyFeatures.
  ///
  /// In en, this message translates to:
  /// **'Safety Features'**
  String get safetyFeatures;

  /// No description provided for @accessibility.
  ///
  /// In en, this message translates to:
  /// **'Accessibility'**
  String get accessibility;

  /// No description provided for @nearbyAttractions.
  ///
  /// In en, this message translates to:
  /// **'Nearby Attractions'**
  String get nearbyAttractions;

  /// No description provided for @transportation.
  ///
  /// In en, this message translates to:
  /// **'Transportation'**
  String get transportation;

  /// No description provided for @checkInInstructions.
  ///
  /// In en, this message translates to:
  /// **'Check-in Instructions'**
  String get checkInInstructions;

  /// No description provided for @houseRules.
  ///
  /// In en, this message translates to:
  /// **'House Rules'**
  String get houseRules;

  /// No description provided for @importantInfo.
  ///
  /// In en, this message translates to:
  /// **'Important Information'**
  String get importantInfo;

  /// No description provided for @instantBook.
  ///
  /// In en, this message translates to:
  /// **'Instant Book'**
  String get instantBook;

  /// No description provided for @verified.
  ///
  /// In en, this message translates to:
  /// **'Verified'**
  String get verified;

  /// No description provided for @newListing.
  ///
  /// In en, this message translates to:
  /// **'New Listing'**
  String get newListing;

  /// No description provided for @rareFind.
  ///
  /// In en, this message translates to:
  /// **'Rare Find'**
  String get rareFind;

  /// No description provided for @guestFavorite.
  ///
  /// In en, this message translates to:
  /// **'Guest Favorite'**
  String get guestFavorite;

  /// No description provided for @topRated.
  ///
  /// In en, this message translates to:
  /// **'Top Rated'**
  String get topRated;

  /// No description provided for @luxuryStay.
  ///
  /// In en, this message translates to:
  /// **'Luxury Stay'**
  String get luxuryStay;

  /// No description provided for @budgetFriendly.
  ///
  /// In en, this message translates to:
  /// **'Budget Friendly'**
  String get budgetFriendly;

  /// No description provided for @familyFriendly.
  ///
  /// In en, this message translates to:
  /// **'Family Friendly'**
  String get familyFriendly;

  /// No description provided for @petFriendly.
  ///
  /// In en, this message translates to:
  /// **'Pet Friendly'**
  String get petFriendly;

  /// No description provided for @workFriendly.
  ///
  /// In en, this message translates to:
  /// **'Work Friendly'**
  String get workFriendly;

  /// No description provided for @partyFriendly.
  ///
  /// In en, this message translates to:
  /// **'Party Friendly'**
  String get partyFriendly;

  /// No description provided for @smokingAllowed.
  ///
  /// In en, this message translates to:
  /// **'Smoking Allowed'**
  String get smokingAllowed;

  /// No description provided for @noSmoking.
  ///
  /// In en, this message translates to:
  /// **'No Smoking'**
  String get noSmoking;

  /// No description provided for @freeWifi.
  ///
  /// In en, this message translates to:
  /// **'Free WiFi'**
  String get freeWifi;

  /// No description provided for @freeParking.
  ///
  /// In en, this message translates to:
  /// **'Free Parking'**
  String get freeParking;

  /// No description provided for @pool.
  ///
  /// In en, this message translates to:
  /// **'Pool'**
  String get pool;

  /// No description provided for @gym.
  ///
  /// In en, this message translates to:
  /// **'Gym'**
  String get gym;

  /// No description provided for @spa.
  ///
  /// In en, this message translates to:
  /// **'Spa'**
  String get spa;

  /// No description provided for @restaurant.
  ///
  /// In en, this message translates to:
  /// **'Restaurant'**
  String get restaurant;

  /// No description provided for @bar.
  ///
  /// In en, this message translates to:
  /// **'Bar'**
  String get bar;

  /// No description provided for @laundry.
  ///
  /// In en, this message translates to:
  /// **'Laundry'**
  String get laundry;

  /// No description provided for @kitchen.
  ///
  /// In en, this message translates to:
  /// **'Kitchen'**
  String get kitchen;

  /// No description provided for @airConditioning.
  ///
  /// In en, this message translates to:
  /// **'Air Conditioning'**
  String get airConditioning;

  /// No description provided for @heating.
  ///
  /// In en, this message translates to:
  /// **'Heating'**
  String get heating;

  /// No description provided for @tv.
  ///
  /// In en, this message translates to:
  /// **'TV'**
  String get tv;

  /// No description provided for @workspace.
  ///
  /// In en, this message translates to:
  /// **'Workspace'**
  String get workspace;

  /// No description provided for @balcony.
  ///
  /// In en, this message translates to:
  /// **'Balcony'**
  String get balcony;

  /// No description provided for @garden.
  ///
  /// In en, this message translates to:
  /// **'Garden'**
  String get garden;

  /// No description provided for @beachAccess.
  ///
  /// In en, this message translates to:
  /// **'Beach Access'**
  String get beachAccess;

  /// No description provided for @mountainView.
  ///
  /// In en, this message translates to:
  /// **'Mountain View'**
  String get mountainView;

  /// No description provided for @cityView.
  ///
  /// In en, this message translates to:
  /// **'City View'**
  String get cityView;

  /// No description provided for @oceanView.
  ///
  /// In en, this message translates to:
  /// **'Ocean View'**
  String get oceanView;

  /// No description provided for @lakeView.
  ///
  /// In en, this message translates to:
  /// **'Lake View'**
  String get lakeView;

  /// No description provided for @gardenView.
  ///
  /// In en, this message translates to:
  /// **'Garden View'**
  String get gardenView;

  /// No description provided for @streetView.
  ///
  /// In en, this message translates to:
  /// **'Street View'**
  String get streetView;

  /// No description provided for @noView.
  ///
  /// In en, this message translates to:
  /// **'No View'**
  String get noView;

  /// No description provided for @bookingSummary.
  ///
  /// In en, this message translates to:
  /// **'Booking Summary'**
  String get bookingSummary;

  /// No description provided for @hostDashboard.
  ///
  /// In en, this message translates to:
  /// **'Host Dashboard'**
  String get hostDashboard;

  /// No description provided for @walletBalance.
  ///
  /// In en, this message translates to:
  /// **'Wallet Balance'**
  String get walletBalance;

  /// No description provided for @totalEarnings.
  ///
  /// In en, this message translates to:
  /// **'Total Earnings'**
  String get totalEarnings;

  /// No description provided for @recentBookings.
  ///
  /// In en, this message translates to:
  /// **'Recent Bookings'**
  String get recentBookings;

  /// No description provided for @recentReviews.
  ///
  /// In en, this message translates to:
  /// **'Recent Reviews'**
  String get recentReviews;

  /// No description provided for @withdraw.
  ///
  /// In en, this message translates to:
  /// **'Withdraw'**
  String get withdraw;

  /// No description provided for @earnings.
  ///
  /// In en, this message translates to:
  /// **'Earnings'**
  String get earnings;

  /// No description provided for @bookingsChart.
  ///
  /// In en, this message translates to:
  /// **'Bookings Chart'**
  String get bookingsChart;

  /// No description provided for @earningsChart.
  ///
  /// In en, this message translates to:
  /// **'Earnings Chart'**
  String get earningsChart;

  /// No description provided for @thisMonth.
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// No description provided for @lastMonth.
  ///
  /// In en, this message translates to:
  /// **'Last Month'**
  String get lastMonth;

  /// No description provided for @thisYear.
  ///
  /// In en, this message translates to:
  /// **'This Year'**
  String get thisYear;

  /// No description provided for @availableBalance.
  ///
  /// In en, this message translates to:
  /// **'Available Balance'**
  String get availableBalance;

  /// No description provided for @pendingEarnings.
  ///
  /// In en, this message translates to:
  /// **'Pending Earnings'**
  String get pendingEarnings;

  /// No description provided for @totalWithdrawn.
  ///
  /// In en, this message translates to:
  /// **'Total Withdrawn'**
  String get totalWithdrawn;

  /// No description provided for @withdrawFunds.
  ///
  /// In en, this message translates to:
  /// **'Withdraw Funds'**
  String get withdrawFunds;

  /// No description provided for @enterAmount.
  ///
  /// In en, this message translates to:
  /// **'Enter Amount'**
  String get enterAmount;

  /// No description provided for @minimumWithdraw.
  ///
  /// In en, this message translates to:
  /// **'Minimum withdrawal: SR 50'**
  String get minimumWithdraw;

  /// No description provided for @withdrawalMethod.
  ///
  /// In en, this message translates to:
  /// **'Withdrawal Method'**
  String get withdrawalMethod;

  /// No description provided for @bankTransfer.
  ///
  /// In en, this message translates to:
  /// **'Bank Transfer'**
  String get bankTransfer;

  /// No description provided for @paypal.
  ///
  /// In en, this message translates to:
  /// **'PayPal'**
  String get paypal;

  /// No description provided for @processing.
  ///
  /// In en, this message translates to:
  /// **'Processing'**
  String get processing;

  /// No description provided for @noRecentBookings.
  ///
  /// In en, this message translates to:
  /// **'No recent bookings'**
  String get noRecentBookings;

  /// No description provided for @noRecentReviews.
  ///
  /// In en, this message translates to:
  /// **'No recent reviews'**
  String get noRecentReviews;

  /// No description provided for @average.
  ///
  /// In en, this message translates to:
  /// **'Average'**
  String get average;

  /// No description provided for @guestName.
  ///
  /// In en, this message translates to:
  /// **'Guest Name'**
  String get guestName;

  /// No description provided for @checkInOut.
  ///
  /// In en, this message translates to:
  /// **'Check-in/out'**
  String get checkInOut;

  /// No description provided for @nightsStayed.
  ///
  /// In en, this message translates to:
  /// **'Nights Stayed'**
  String get nightsStayed;

  /// No description provided for @earningsOverview.
  ///
  /// In en, this message translates to:
  /// **'Earnings Overview'**
  String get earningsOverview;

  /// No description provided for @bookingsOverview.
  ///
  /// In en, this message translates to:
  /// **'Bookings Overview'**
  String get bookingsOverview;

  /// No description provided for @last6Months.
  ///
  /// In en, this message translates to:
  /// **'Last 6 Months'**
  String get last6Months;

  /// No description provided for @guestComment.
  ///
  /// In en, this message translates to:
  /// **'Guest Comment'**
  String get guestComment;

  /// No description provided for @dates.
  ///
  /// In en, this message translates to:
  /// **'Dates'**
  String get dates;

  /// No description provided for @nights.
  ///
  /// In en, this message translates to:
  /// **'Nights'**
  String get nights;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// No description provided for @enterSearchTerm.
  ///
  /// In en, this message translates to:
  /// **'Enter search term...'**
  String get enterSearchTerm;

  /// No description provided for @noSearchResults.
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noSearchResults;

  /// No description provided for @noTitle.
  ///
  /// In en, this message translates to:
  /// **'No title'**
  String get noTitle;

  /// No description provided for @noDescription.
  ///
  /// In en, this message translates to:
  /// **'No description'**
  String get noDescription;

  /// No description provided for @tryDifferentSearch.
  ///
  /// In en, this message translates to:
  /// **'Try searching with different words'**
  String get tryDifferentSearch;

  /// No description provided for @searchResults.
  ///
  /// In en, this message translates to:
  /// **'Search Results'**
  String get searchResults;

  /// No description provided for @noBookingsMessage.
  ///
  /// In en, this message translates to:
  /// **'No recent bookings'**
  String get noBookingsMessage;

  /// No description provided for @noReviewsMessage.
  ///
  /// In en, this message translates to:
  /// **'No recent reviews'**
  String get noReviewsMessage;

  /// No description provided for @hostModeDescription.
  ///
  /// In en, this message translates to:
  /// **'Enable host mode to manage your properties'**
  String get hostModeDescription;

  /// No description provided for @logoutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirmation;

  /// No description provided for @mustLogin.
  ///
  /// In en, this message translates to:
  /// **'You must login'**
  String get mustLogin;

  /// No description provided for @mustLoginDescription.
  ///
  /// In en, this message translates to:
  /// **'Please login to view your profile'**
  String get mustLoginDescription;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @confirmReservation.
  ///
  /// In en, this message translates to:
  /// **'Confirm Reservation'**
  String get confirmReservation;

  /// No description provided for @unitDetails.
  ///
  /// In en, this message translates to:
  /// **'Unit Details'**
  String get unitDetails;

  /// No description provided for @unitName.
  ///
  /// In en, this message translates to:
  /// **'Unit Name'**
  String get unitName;

  /// No description provided for @numberOfDays.
  ///
  /// In en, this message translates to:
  /// **'Number of Days'**
  String get numberOfDays;

  /// No description provided for @reservationFrom.
  ///
  /// In en, this message translates to:
  /// **'Reservation From'**
  String get reservationFrom;

  /// No description provided for @reservationTo.
  ///
  /// In en, this message translates to:
  /// **'Reservation To'**
  String get reservationTo;

  /// No description provided for @priceDetails.
  ///
  /// In en, this message translates to:
  /// **'Price Details'**
  String get priceDetails;

  /// No description provided for @finalPrice.
  ///
  /// In en, this message translates to:
  /// **'Final Price'**
  String get finalPrice;

  /// No description provided for @priceBredown.
  ///
  /// In en, this message translates to:
  /// **'Price Breakdown'**
  String get priceBredown;

  /// No description provided for @priceType.
  ///
  /// In en, this message translates to:
  /// **'Price Type'**
  String get priceType;

  /// No description provided for @weekendPrice.
  ///
  /// In en, this message translates to:
  /// **'Weekend Price'**
  String get weekendPrice;

  /// No description provided for @normalDays.
  ///
  /// In en, this message translates to:
  /// **'Normal Days'**
  String get normalDays;

  /// No description provided for @weekendDays.
  ///
  /// In en, this message translates to:
  /// **'Weekend Days'**
  String get weekendDays;

  /// No description provided for @confirmBooking.
  ///
  /// In en, this message translates to:
  /// **'Confirm Booking'**
  String get confirmBooking;

  /// No description provided for @reservationConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Reservation confirmed successfully!'**
  String get reservationConfirmed;

  /// No description provided for @reservationFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to confirm reservation'**
  String get reservationFailed;

  /// No description provided for @invalidDate.
  ///
  /// In en, this message translates to:
  /// **'Invalid date'**
  String get invalidDate;

  /// No description provided for @notAvailable.
  ///
  /// In en, this message translates to:
  /// **'Not available'**
  String get notAvailable;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// No description provided for @reviewsOverview.
  ///
  /// In en, this message translates to:
  /// **'Reviews Overview'**
  String get reviewsOverview;

  /// No description provided for @totalReviews.
  ///
  /// In en, this message translates to:
  /// **'Total Reviews'**
  String get totalReviews;

  /// No description provided for @allReviews.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get allReviews;

  /// No description provided for @highRated.
  ///
  /// In en, this message translates to:
  /// **'High Rated'**
  String get highRated;

  /// No description provided for @noReviewsFound.
  ///
  /// In en, this message translates to:
  /// **'No reviews found'**
  String get noReviewsFound;

  /// No description provided for @noReviewsMatchFilter.
  ///
  /// In en, this message translates to:
  /// **'No reviews match the selected filter'**
  String get noReviewsMatchFilter;

  /// No description provided for @foundHelpful.
  ///
  /// In en, this message translates to:
  /// **'people found this review helpful'**
  String get foundHelpful;

  /// No description provided for @selectReservationDate.
  ///
  /// In en, this message translates to:
  /// **'Select Reservation Date'**
  String get selectReservationDate;

  /// No description provided for @reviewReservation.
  ///
  /// In en, this message translates to:
  /// **'Review Reservation'**
  String get reviewReservation;

  /// No description provided for @pleaseSelectBothDates.
  ///
  /// In en, this message translates to:
  /// **'Please select both dates'**
  String get pleaseSelectBothDates;

  /// No description provided for @selectedPeriodNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'The selected period is not available, please choose another period.'**
  String get selectedPeriodNotAvailable;

  /// No description provided for @errorFetchingInfo.
  ///
  /// In en, this message translates to:
  /// **'An error occurred while fetching information'**
  String get errorFetchingInfo;

  /// No description provided for @failedToLoadVideo.
  ///
  /// In en, this message translates to:
  /// **'Failed to load video'**
  String get failedToLoadVideo;

  /// No description provided for @gatherPoint.
  ///
  /// In en, this message translates to:
  /// **'Gather Point'**
  String get gatherPoint;

  /// No description provided for @muteVideo.
  ///
  /// In en, this message translates to:
  /// **'Mute Video'**
  String get muteVideo;

  /// No description provided for @unmuteVideo.
  ///
  /// In en, this message translates to:
  /// **'Unmute Video'**
  String get unmuteVideo;

  /// No description provided for @playVideo.
  ///
  /// In en, this message translates to:
  /// **'Play Video'**
  String get playVideo;

  /// No description provided for @pauseVideo.
  ///
  /// In en, this message translates to:
  /// **'Pause Video'**
  String get pauseVideo;

  /// No description provided for @comment.
  ///
  /// In en, this message translates to:
  /// **'Comment'**
  String get comment;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @comments.
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// No description provided for @writeComment.
  ///
  /// In en, this message translates to:
  /// **'Write a comment...'**
  String get writeComment;

  /// No description provided for @postComment.
  ///
  /// In en, this message translates to:
  /// **'Post Comment'**
  String get postComment;

  /// No description provided for @noComments.
  ///
  /// In en, this message translates to:
  /// **'No comments yet'**
  String get noComments;

  /// No description provided for @commentPosted.
  ///
  /// In en, this message translates to:
  /// **'Comment posted successfully'**
  String get commentPosted;

  /// No description provided for @commentFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to post comment'**
  String get commentFailed;

  /// No description provided for @deleteComment.
  ///
  /// In en, this message translates to:
  /// **'Delete Comment'**
  String get deleteComment;

  /// No description provided for @editComment.
  ///
  /// In en, this message translates to:
  /// **'Edit Comment'**
  String get editComment;

  /// No description provided for @replyToComment.
  ///
  /// In en, this message translates to:
  /// **'Reply to Comment'**
  String get replyToComment;

  /// No description provided for @showComments.
  ///
  /// In en, this message translates to:
  /// **'Show Comments'**
  String get showComments;

  /// No description provided for @hideComments.
  ///
  /// In en, this message translates to:
  /// **'Hide Comments'**
  String get hideComments;

  /// No description provided for @searchReels.
  ///
  /// In en, this message translates to:
  /// **'Search Reels'**
  String get searchReels;

  /// No description provided for @filterReels.
  ///
  /// In en, this message translates to:
  /// **'Filter Reels'**
  String get filterReels;

  /// No description provided for @allCategories.
  ///
  /// In en, this message translates to:
  /// **'All Categories'**
  String get allCategories;

  /// No description provided for @sortBy.
  ///
  /// In en, this message translates to:
  /// **'Sort By'**
  String get sortBy;

  /// No description provided for @newest.
  ///
  /// In en, this message translates to:
  /// **'Newest'**
  String get newest;

  /// No description provided for @oldest.
  ///
  /// In en, this message translates to:
  /// **'Oldest'**
  String get oldest;

  /// No description provided for @mostLiked.
  ///
  /// In en, this message translates to:
  /// **'Most Liked'**
  String get mostLiked;

  /// No description provided for @mostCommented.
  ///
  /// In en, this message translates to:
  /// **'Most Commented'**
  String get mostCommented;

  /// No description provided for @applyFilter.
  ///
  /// In en, this message translates to:
  /// **'Apply Filter'**
  String get applyFilter;

  /// No description provided for @clearFilter.
  ///
  /// In en, this message translates to:
  /// **'Clear Filter'**
  String get clearFilter;

  /// No description provided for @noResultsFound.
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResultsFound;

  /// No description provided for @additionalSettings.
  ///
  /// In en, this message translates to:
  /// **'Additional Settings'**
  String get additionalSettings;

  /// No description provided for @notificationSettings.
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettings;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// No description provided for @filterResults.
  ///
  /// In en, this message translates to:
  /// **'Filter Results'**
  String get filterResults;

  /// No description provided for @priceRange.
  ///
  /// In en, this message translates to:
  /// **'Price Range'**
  String get priceRange;

  /// No description provided for @minimumRating.
  ///
  /// In en, this message translates to:
  /// **'Minimum Rating'**
  String get minimumRating;

  /// No description provided for @resetFilters.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get resetFilters;

  /// No description provided for @applyFilters.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get applyFilters;

  /// No description provided for @tryDifferentSearchCriteria.
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your search criteria'**
  String get tryDifferentSearchCriteria;

  /// No description provided for @priceLowToHigh.
  ///
  /// In en, this message translates to:
  /// **'Price: Low to High'**
  String get priceLowToHigh;

  /// No description provided for @priceHighToLow.
  ///
  /// In en, this message translates to:
  /// **'Price: High to Low'**
  String get priceHighToLow;

  /// No description provided for @ratingHighToLow.
  ///
  /// In en, this message translates to:
  /// **'Rating: High to Low'**
  String get ratingHighToLow;

  /// No description provided for @ratingLowToHigh.
  ///
  /// In en, this message translates to:
  /// **'Rating: Low to High'**
  String get ratingLowToHigh;

  /// No description provided for @popular.
  ///
  /// In en, this message translates to:
  /// **'Popular'**
  String get popular;

  /// No description provided for @privacyAndSecurity.
  ///
  /// In en, this message translates to:
  /// **'Privacy and security settings'**
  String get privacyAndSecurity;

  /// No description provided for @appInformation.
  ///
  /// In en, this message translates to:
  /// **'App information and version'**
  String get appInformation;

  /// No description provided for @pushNotifications.
  ///
  /// In en, this message translates to:
  /// **'Push Notifications'**
  String get pushNotifications;

  /// No description provided for @eventNotifications.
  ///
  /// In en, this message translates to:
  /// **'Event Notifications'**
  String get eventNotifications;

  /// No description provided for @messageNotifications.
  ///
  /// In en, this message translates to:
  /// **'Message Notifications'**
  String get messageNotifications;

  /// No description provided for @marketingNotifications.
  ///
  /// In en, this message translates to:
  /// **'Marketing Notifications'**
  String get marketingNotifications;

  /// No description provided for @enableAllNotifications.
  ///
  /// In en, this message translates to:
  /// **'Enable or disable all notifications'**
  String get enableAllNotifications;

  /// No description provided for @newEventsAndUpdates.
  ///
  /// In en, this message translates to:
  /// **'Notifications about new events and updates'**
  String get newEventsAndUpdates;

  /// No description provided for @newMessagesAndChats.
  ///
  /// In en, this message translates to:
  /// **'Notifications for new messages and conversations'**
  String get newMessagesAndChats;

  /// No description provided for @offersAndMarketing.
  ///
  /// In en, this message translates to:
  /// **'Notifications about offers and marketing news'**
  String get offersAndMarketing;

  /// No description provided for @testNotification.
  ///
  /// In en, this message translates to:
  /// **'Test Notification'**
  String get testNotification;

  /// No description provided for @sendTestNotification.
  ///
  /// In en, this message translates to:
  /// **'Send Test Notification'**
  String get sendTestNotification;

  /// No description provided for @notificationPermissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Notification Permission Required'**
  String get notificationPermissionRequired;

  /// No description provided for @enableNotificationsInSettings.
  ///
  /// In en, this message translates to:
  /// **'Please enable notifications in device settings'**
  String get enableNotificationsInSettings;

  /// No description provided for @openSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get openSettings;

  /// No description provided for @dataAndPrivacy.
  ///
  /// In en, this message translates to:
  /// **'Data & Privacy'**
  String get dataAndPrivacy;

  /// No description provided for @dataCollection.
  ///
  /// In en, this message translates to:
  /// **'Data Collection'**
  String get dataCollection;

  /// No description provided for @thirdPartySharing.
  ///
  /// In en, this message translates to:
  /// **'Third Party Sharing'**
  String get thirdPartySharing;

  /// No description provided for @dataRetention.
  ///
  /// In en, this message translates to:
  /// **'Data Retention'**
  String get dataRetention;

  /// No description provided for @yourRights.
  ///
  /// In en, this message translates to:
  /// **'Your Rights'**
  String get yourRights;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @deleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// No description provided for @dataCollectionDesc.
  ///
  /// In en, this message translates to:
  /// **'How we collect and use your data'**
  String get dataCollectionDesc;

  /// No description provided for @thirdPartySharingDesc.
  ///
  /// In en, this message translates to:
  /// **'Information about data sharing with partners'**
  String get thirdPartySharingDesc;

  /// No description provided for @dataRetentionDesc.
  ///
  /// In en, this message translates to:
  /// **'How long we keep your data'**
  String get dataRetentionDesc;

  /// No description provided for @yourRightsDesc.
  ///
  /// In en, this message translates to:
  /// **'Your privacy rights and how to exercise them'**
  String get yourRightsDesc;

  /// No description provided for @contactUsDesc.
  ///
  /// In en, this message translates to:
  /// **'Get in touch with our support team'**
  String get contactUsDesc;

  /// No description provided for @deleteAccountDesc.
  ///
  /// In en, this message translates to:
  /// **'Permanently delete your account and data'**
  String get deleteAccountDesc;

  /// No description provided for @proceedWithGoogle.
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get proceedWithGoogle;

  /// No description provided for @proceedWithApple.
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get proceedWithApple;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// No description provided for @welcomeGuest.
  ///
  /// In en, this message translates to:
  /// **'Welcome our guest... continue'**
  String get welcomeGuest;

  /// No description provided for @proceedAsGuest.
  ///
  /// In en, this message translates to:
  /// **'Continue as Guest'**
  String get proceedAsGuest;

  /// No description provided for @proceedWithPhone.
  ///
  /// In en, this message translates to:
  /// **'Continue with your phone number'**
  String get proceedWithPhone;

  /// No description provided for @verifyPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Verify your phone number'**
  String get verifyPhoneNumber;

  /// No description provided for @enterVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Enter verification code'**
  String get enterVerificationCode;

  /// No description provided for @verificationCodeSent.
  ///
  /// In en, this message translates to:
  /// **'A 4-digit code has been sent to your phone'**
  String get verificationCodeSent;

  /// No description provided for @proceedLabel.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get proceedLabel;

  /// No description provided for @viewAllBookings.
  ///
  /// In en, this message translates to:
  /// **'View All Bookings'**
  String get viewAllBookings;

  /// No description provided for @viewAllReviews.
  ///
  /// In en, this message translates to:
  /// **'View All Reviews'**
  String get viewAllReviews;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get enterPhoneNumber;

  /// No description provided for @phoneNumberHint.
  ///
  /// In en, this message translates to:
  /// **'5xxxxxxxx'**
  String get phoneNumberHint;

  /// No description provided for @pleaseEnterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter phone number'**
  String get pleaseEnterPhoneNumber;

  /// No description provided for @pleaseCheckPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please check phone number'**
  String get pleaseCheckPhoneNumber;

  /// No description provided for @invalidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number'**
  String get invalidPhoneNumber;

  /// No description provided for @phoneNumberRequired.
  ///
  /// In en, this message translates to:
  /// **'Phone number is required'**
  String get phoneNumberRequired;

  /// No description provided for @resendCode.
  ///
  /// In en, this message translates to:
  /// **'Resend code'**
  String get resendCode;

  /// No description provided for @didntReceiveCode.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t receive the code?'**
  String get didntReceiveCode;

  /// No description provided for @verificationFailed.
  ///
  /// In en, this message translates to:
  /// **'Verification failed'**
  String get verificationFailed;

  /// No description provided for @invalidCode.
  ///
  /// In en, this message translates to:
  /// **'Invalid code'**
  String get invalidCode;

  /// No description provided for @codeExpired.
  ///
  /// In en, this message translates to:
  /// **'Code expired'**
  String get codeExpired;

  /// No description provided for @continueButton.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// No description provided for @exploreAllCategoriesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Explore all available categories'**
  String get exploreAllCategoriesSubtitle;

  /// No description provided for @basicInformation.
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get basicInformation;

  /// No description provided for @enterPropertyTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter property title'**
  String get enterPropertyTitle;

  /// No description provided for @pleaseEnterPropertyTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter property title'**
  String get pleaseEnterPropertyTitle;

  /// No description provided for @enterPropertyDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter property description'**
  String get enterPropertyDescription;

  /// No description provided for @pleaseEnterDescription.
  ///
  /// In en, this message translates to:
  /// **'Please enter description'**
  String get pleaseEnterDescription;

  /// No description provided for @pleaseEnterPrice.
  ///
  /// In en, this message translates to:
  /// **'Please enter price'**
  String get pleaseEnterPrice;

  /// No description provided for @pleaseEnterValidPrice.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid price'**
  String get pleaseEnterValidPrice;

  /// No description provided for @maxGuests.
  ///
  /// In en, this message translates to:
  /// **'Max Guests'**
  String get maxGuests;

  /// No description provided for @pleaseEnterMaxGuests.
  ///
  /// In en, this message translates to:
  /// **'Please enter max guests'**
  String get pleaseEnterMaxGuests;

  /// No description provided for @pleaseEnterValidNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid number'**
  String get pleaseEnterValidNumber;

  /// No description provided for @pleaseEnterBedrooms.
  ///
  /// In en, this message translates to:
  /// **'Please enter bedrooms'**
  String get pleaseEnterBedrooms;

  /// No description provided for @pleaseBathrooms.
  ///
  /// In en, this message translates to:
  /// **'Please enter bathrooms'**
  String get pleaseBathrooms;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @pleaseSelectCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select category'**
  String get pleaseSelectCategory;

  /// No description provided for @facilities.
  ///
  /// In en, this message translates to:
  /// **'Facilities'**
  String get facilities;

  /// No description provided for @media.
  ///
  /// In en, this message translates to:
  /// **'Media'**
  String get media;

  /// No description provided for @mainImage.
  ///
  /// In en, this message translates to:
  /// **'Main Image'**
  String get mainImage;

  /// No description provided for @video.
  ///
  /// In en, this message translates to:
  /// **'Video'**
  String get video;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @addImages.
  ///
  /// In en, this message translates to:
  /// **'Add Images'**
  String get addImages;

  /// No description provided for @bookingRules.
  ///
  /// In en, this message translates to:
  /// **'Booking Rules'**
  String get bookingRules;

  /// No description provided for @enterBookingRules.
  ///
  /// In en, this message translates to:
  /// **'Enter booking rules'**
  String get enterBookingRules;

  /// No description provided for @enterCancellationRules.
  ///
  /// In en, this message translates to:
  /// **'Enter cancellation rules'**
  String get enterCancellationRules;

  /// No description provided for @reviewSubmittedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Review submitted successfully'**
  String get reviewSubmittedSuccessfully;

  /// No description provided for @submitReview.
  ///
  /// In en, this message translates to:
  /// **'Submit Review'**
  String get submitReview;

  /// No description provided for @loginRequiredForReservation.
  ///
  /// In en, this message translates to:
  /// **'You need to login to make a reservation. Guest users can also make reservations with limited features.'**
  String get loginRequiredForReservation;

  /// No description provided for @loginRequiredForFavorites.
  ///
  /// In en, this message translates to:
  /// **'You need to login to add items to your favorites list.'**
  String get loginRequiredForFavorites;

  /// No description provided for @loginRequiredForReviews.
  ///
  /// In en, this message translates to:
  /// **'You need to login to write reviews and share your experience.'**
  String get loginRequiredForReviews;

  /// No description provided for @guestModeInfo.
  ///
  /// In en, this message translates to:
  /// **'As a guest, you can browse and make reservations, but some features like favorites and reviews require an account.'**
  String get guestModeInfo;

  /// No description provided for @guestReservation.
  ///
  /// In en, this message translates to:
  /// **'Guest Reservation'**
  String get guestReservation;

  /// No description provided for @guestReservationMessage.
  ///
  /// In en, this message translates to:
  /// **'You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features.'**
  String get guestReservationMessage;

  /// No description provided for @guestLimitations.
  ///
  /// In en, this message translates to:
  /// **'Guest Limitations:'**
  String get guestLimitations;

  /// No description provided for @guestLimitationsDetails.
  ///
  /// In en, this message translates to:
  /// **'• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management'**
  String get guestLimitationsDetails;

  /// No description provided for @loginForBetterExperience.
  ///
  /// In en, this message translates to:
  /// **'Login for Better Experience'**
  String get loginForBetterExperience;

  /// No description provided for @continueAsGuest.
  ///
  /// In en, this message translates to:
  /// **'Continue as Guest'**
  String get continueAsGuest;

  /// No description provided for @featureUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Feature Unavailable'**
  String get featureUnavailable;

  /// No description provided for @featureRequiresLogin.
  ///
  /// In en, this message translates to:
  /// **'This feature requires you to login. Please create an account or login to access this feature.'**
  String get featureRequiresLogin;

  /// No description provided for @guest.
  ///
  /// In en, this message translates to:
  /// **'Guest'**
  String get guest;

  /// No description provided for @retryConnection.
  ///
  /// In en, this message translates to:
  /// **'Retry Connection'**
  String get retryConnection;

  /// No description provided for @connectionError.
  ///
  /// In en, this message translates to:
  /// **'Connection Error'**
  String get connectionError;

  /// No description provided for @serverError.
  ///
  /// In en, this message translates to:
  /// **'Server Error'**
  String get serverError;

  /// No description provided for @unknownError.
  ///
  /// In en, this message translates to:
  /// **'Unknown Error'**
  String get unknownError;

  /// No description provided for @loadingData.
  ///
  /// In en, this message translates to:
  /// **'Loading data...'**
  String get loadingData;

  /// No description provided for @refreshData.
  ///
  /// In en, this message translates to:
  /// **'Refresh Data'**
  String get refreshData;

  /// No description provided for @noInternetConnection.
  ///
  /// In en, this message translates to:
  /// **'No Internet Connection'**
  String get noInternetConnection;

  /// No description provided for @checkInternetConnection.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again'**
  String get checkInternetConnection;

  /// No description provided for @dataLoadFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to load data'**
  String get dataLoadFailed;

  /// No description provided for @pullToRefresh.
  ///
  /// In en, this message translates to:
  /// **'Pull to refresh'**
  String get pullToRefresh;

  /// No description provided for @ofPreposition.
  ///
  /// In en, this message translates to:
  /// **'of'**
  String get ofPreposition;

  /// No description provided for @tourismPermitNumber.
  ///
  /// In en, this message translates to:
  /// **'Tourism Permit Number *'**
  String get tourismPermitNumber;

  /// No description provided for @dataLoadError.
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get dataLoadError;

  /// No description provided for @noDataAvailable.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noDataAvailable;

  /// No description provided for @reviewsCount.
  ///
  /// In en, this message translates to:
  /// **'review'**
  String get reviewsCount;

  /// No description provided for @sarPerNight.
  ///
  /// In en, this message translates to:
  /// **'SR/night'**
  String get sarPerNight;

  /// No description provided for @freeWifiArabic.
  ///
  /// In en, this message translates to:
  /// **'Free WiFi'**
  String get freeWifiArabic;

  /// No description provided for @hostedBy.
  ///
  /// In en, this message translates to:
  /// **'Hosted by'**
  String get hostedBy;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'year'**
  String get year;

  /// No description provided for @years.
  ///
  /// In en, this message translates to:
  /// **'years'**
  String get years;

  /// No description provided for @inHosting.
  ///
  /// In en, this message translates to:
  /// **'hosting'**
  String get inHosting;

  /// No description provided for @newHost.
  ///
  /// In en, this message translates to:
  /// **'New host'**
  String get newHost;

  /// No description provided for @noDescriptionAvailable.
  ///
  /// In en, this message translates to:
  /// **'No description available.'**
  String get noDescriptionAvailable;

  /// No description provided for @guestRating.
  ///
  /// In en, this message translates to:
  /// **'Guest review'**
  String get guestRating;

  /// No description provided for @march2024.
  ///
  /// In en, this message translates to:
  /// **'March 2024'**
  String get march2024;

  /// No description provided for @sampleReviewText.
  ///
  /// In en, this message translates to:
  /// **'Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful.'**
  String get sampleReviewText;

  /// No description provided for @showAllReviews.
  ///
  /// In en, this message translates to:
  /// **'Show all'**
  String get showAllReviews;

  /// No description provided for @sar.
  ///
  /// In en, this message translates to:
  /// **'SR'**
  String get sar;

  /// Currency symbol for Saudi Riyal
  ///
  /// In en, this message translates to:
  /// **'SR'**
  String get currencySymbol;

  /// Currency code for Saudi Riyal
  ///
  /// In en, this message translates to:
  /// **'SAR'**
  String get currencyCode;

  /// Price format with currency symbol
  ///
  /// In en, this message translates to:
  /// **'{price} SR'**
  String priceWithCurrency(String price);

  /// Minimum withdrawal amount with currency
  ///
  /// In en, this message translates to:
  /// **'Minimum withdrawal: SR 50'**
  String get minimumWithdrawAmount;

  /// Label for smart entry facility in place quick stats.
  ///
  /// In en, this message translates to:
  /// **'Smart Entry'**
  String get smartEntry;

  /// No description provided for @knowledge.
  ///
  /// In en, this message translates to:
  /// **'Knowledge'**
  String get knowledge;

  /// No description provided for @previousTrips.
  ///
  /// In en, this message translates to:
  /// **'Previous Trips'**
  String get previousTrips;

  /// No description provided for @joinAsHost.
  ///
  /// In en, this message translates to:
  /// **'Join as Host'**
  String get joinAsHost;

  /// No description provided for @joinAsHostSubtitle.
  ///
  /// In en, this message translates to:
  /// **'It\'s easy to start hosting and earn extra income'**
  String get joinAsHostSubtitle;

  /// No description provided for @accountSettings.
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get accountSettings;

  /// No description provided for @requestHelp.
  ///
  /// In en, this message translates to:
  /// **'Request Help'**
  String get requestHelp;

  /// No description provided for @viewProfile.
  ///
  /// In en, this message translates to:
  /// **'View Profile'**
  String get viewProfile;

  /// No description provided for @referHost.
  ///
  /// In en, this message translates to:
  /// **'Refer Host'**
  String get referHost;

  /// No description provided for @legal.
  ///
  /// In en, this message translates to:
  /// **'Legal'**
  String get legal;

  /// No description provided for @previousTrip.
  ///
  /// In en, this message translates to:
  /// **'Previous Trip'**
  String get previousTrip;

  /// No description provided for @yearsOnAirbnb.
  ///
  /// In en, this message translates to:
  /// **'Years on Airbnb'**
  String get yearsOnAirbnb;

  /// No description provided for @cancellationPolicyNote.
  ///
  /// In en, this message translates to:
  /// **'Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy.'**
  String get cancellationPolicyNote;

  /// No description provided for @selectCancellationPolicy.
  ///
  /// In en, this message translates to:
  /// **'Select Cancellation Policy'**
  String get selectCancellationPolicy;

  /// No description provided for @shortTermBookings.
  ///
  /// In en, this message translates to:
  /// **'Short-term bookings (≤28 days)'**
  String get shortTermBookings;

  /// No description provided for @longTermBookings.
  ///
  /// In en, this message translates to:
  /// **'Long-term bookings (>28 days)'**
  String get longTermBookings;

  /// No description provided for @flexiblePolicy.
  ///
  /// In en, this message translates to:
  /// **'Flexible'**
  String get flexiblePolicy;

  /// No description provided for @moderatePolicy.
  ///
  /// In en, this message translates to:
  /// **'Moderate'**
  String get moderatePolicy;

  /// No description provided for @strictPolicy.
  ///
  /// In en, this message translates to:
  /// **'Strict'**
  String get strictPolicy;

  /// No description provided for @refundPercentage.
  ///
  /// In en, this message translates to:
  /// **'Refund Percentage'**
  String get refundPercentage;

  /// No description provided for @cancellationWindow.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Window'**
  String get cancellationWindow;

  /// No description provided for @bookingWindow.
  ///
  /// In en, this message translates to:
  /// **'Booking Window'**
  String get bookingWindow;

  /// No description provided for @minimumNotice.
  ///
  /// In en, this message translates to:
  /// **'Minimum Notice'**
  String get minimumNotice;

  /// No description provided for @serviceFeeRefundable.
  ///
  /// In en, this message translates to:
  /// **'Service fee refundable'**
  String get serviceFeeRefundable;

  /// No description provided for @serviceFeeNotRefundable.
  ///
  /// In en, this message translates to:
  /// **'Service fee not refundable'**
  String get serviceFeeNotRefundable;

  /// No description provided for @cleaningFeeRefundable.
  ///
  /// In en, this message translates to:
  /// **'Cleaning fee refundable'**
  String get cleaningFeeRefundable;

  /// No description provided for @cleaningFeeNotRefundable.
  ///
  /// In en, this message translates to:
  /// **'Cleaning fee not refundable'**
  String get cleaningFeeNotRefundable;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'hours'**
  String get hours;

  /// No description provided for @policyDescription.
  ///
  /// In en, this message translates to:
  /// **'Policy Description'**
  String get policyDescription;

  /// No description provided for @pleaseSelectPropertyType.
  ///
  /// In en, this message translates to:
  /// **'Please select property type'**
  String get pleaseSelectPropertyType;

  /// No description provided for @pleaseSelectCancellationPolicy.
  ///
  /// In en, this message translates to:
  /// **'Please select cancellation policy'**
  String get pleaseSelectCancellationPolicy;

  /// No description provided for @selectLocation.
  ///
  /// In en, this message translates to:
  /// **'Select Location'**
  String get selectLocation;

  /// No description provided for @confirmLocation.
  ///
  /// In en, this message translates to:
  /// **'Confirm Location'**
  String get confirmLocation;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @propertyPreview.
  ///
  /// In en, this message translates to:
  /// **'Property Preview'**
  String get propertyPreview;

  /// No description provided for @publishProperty.
  ///
  /// In en, this message translates to:
  /// **'Publish Property'**
  String get publishProperty;

  /// No description provided for @policies.
  ///
  /// In en, this message translates to:
  /// **'Policies'**
  String get policies;

  /// No description provided for @listView.
  ///
  /// In en, this message translates to:
  /// **'List View'**
  String get listView;

  /// No description provided for @gridView.
  ///
  /// In en, this message translates to:
  /// **'Grid View'**
  String get gridView;

  /// No description provided for @cancelSelection.
  ///
  /// In en, this message translates to:
  /// **'Cancel Selection'**
  String get cancelSelection;

  /// No description provided for @selectMultiple.
  ///
  /// In en, this message translates to:
  /// **'Select Multiple'**
  String get selectMultiple;

  /// No description provided for @addNewListing.
  ///
  /// In en, this message translates to:
  /// **'Add New Listing'**
  String get addNewListing;

  /// No description provided for @dashboardOverview.
  ///
  /// In en, this message translates to:
  /// **'Dashboard Overview'**
  String get dashboardOverview;

  /// No description provided for @totalRevenue.
  ///
  /// In en, this message translates to:
  /// **'Total Revenue'**
  String get totalRevenue;

  /// No description provided for @averageRating.
  ///
  /// In en, this message translates to:
  /// **'Average Rating'**
  String get averageRating;

  /// No description provided for @activeListings.
  ///
  /// In en, this message translates to:
  /// **'Active Listings'**
  String get activeListings;

  /// No description provided for @occupancyRate.
  ///
  /// In en, this message translates to:
  /// **'Occupancy Rate'**
  String get occupancyRate;

  /// No description provided for @conversionRate.
  ///
  /// In en, this message translates to:
  /// **'Conversion Rate'**
  String get conversionRate;

  /// No description provided for @allListings.
  ///
  /// In en, this message translates to:
  /// **'All Listings'**
  String get allListings;

  /// No description provided for @inactiveListings.
  ///
  /// In en, this message translates to:
  /// **'Inactive Listings'**
  String get inactiveListings;

  /// No description provided for @drafts.
  ///
  /// In en, this message translates to:
  /// **'Drafts'**
  String get drafts;

  /// No description provided for @pendingReview.
  ///
  /// In en, this message translates to:
  /// **'Pending Review'**
  String get pendingReview;

  /// No description provided for @topPerforming.
  ///
  /// In en, this message translates to:
  /// **'Top Performing'**
  String get topPerforming;

  /// No description provided for @needsAttention.
  ///
  /// In en, this message translates to:
  /// **'Needs Attention'**
  String get needsAttention;

  /// No description provided for @searchListings.
  ///
  /// In en, this message translates to:
  /// **'Search listings...'**
  String get searchListings;

  /// No description provided for @clearFilters.
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get clearFilters;

  /// No description provided for @noListingsYet.
  ///
  /// In en, this message translates to:
  /// **'No Listings Yet'**
  String get noListingsYet;

  /// No description provided for @noListingsDescription.
  ///
  /// In en, this message translates to:
  /// **'Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!'**
  String get noListingsDescription;

  /// No description provided for @createFirstListing.
  ///
  /// In en, this message translates to:
  /// **'Create Your First Listing'**
  String get createFirstListing;

  /// No description provided for @hostingTips.
  ///
  /// In en, this message translates to:
  /// **'Hosting Tips'**
  String get hostingTips;

  /// No description provided for @tip1.
  ///
  /// In en, this message translates to:
  /// **'Add high-quality photos to attract more guests'**
  String get tip1;

  /// No description provided for @tip2.
  ///
  /// In en, this message translates to:
  /// **'Write a detailed description of your property'**
  String get tip2;

  /// No description provided for @tip3.
  ///
  /// In en, this message translates to:
  /// **'Set competitive pricing for your area'**
  String get tip3;

  /// No description provided for @needHelp.
  ///
  /// In en, this message translates to:
  /// **'Need help?'**
  String get needHelp;

  /// No description provided for @contactSupport.
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get contactSupport;

  /// No description provided for @errorLoadingListings.
  ///
  /// In en, this message translates to:
  /// **'Error Loading Listings'**
  String get errorLoadingListings;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @errorPersistsContact.
  ///
  /// In en, this message translates to:
  /// **'If the error persists, please contact support'**
  String get errorPersistsContact;

  /// No description provided for @listingStatus.
  ///
  /// In en, this message translates to:
  /// **'Listing Status'**
  String get listingStatus;

  /// No description provided for @publishListing.
  ///
  /// In en, this message translates to:
  /// **'Publish Listing'**
  String get publishListing;

  /// No description provided for @editListing.
  ///
  /// In en, this message translates to:
  /// **'Edit Listing'**
  String get editListing;

  /// No description provided for @deactivate.
  ///
  /// In en, this message translates to:
  /// **'Deactivate'**
  String get deactivate;

  /// No description provided for @activate.
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get activate;

  /// No description provided for @editWhilePending.
  ///
  /// In en, this message translates to:
  /// **'Edit While Pending'**
  String get editWhilePending;

  /// No description provided for @rejectionReason.
  ///
  /// In en, this message translates to:
  /// **'Rejection Reason'**
  String get rejectionReason;

  /// No description provided for @pendingReservations.
  ///
  /// In en, this message translates to:
  /// **'Pending Reservations'**
  String get pendingReservations;

  /// No description provided for @viewReservations.
  ///
  /// In en, this message translates to:
  /// **'View Reservations'**
  String get viewReservations;

  /// No description provided for @activeStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Your listing is live and visible to guests'**
  String get activeStatusDescription;

  /// No description provided for @inactiveStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Your listing is hidden from guests'**
  String get inactiveStatusDescription;

  /// No description provided for @draftStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Your listing is saved but not published yet'**
  String get draftStatusDescription;

  /// No description provided for @pendingStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Your listing is under review by our team'**
  String get pendingStatusDescription;

  /// No description provided for @suspendedStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Your listing has been suspended'**
  String get suspendedStatusDescription;

  /// No description provided for @unknownStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Unknown status'**
  String get unknownStatusDescription;

  /// No description provided for @publishListingConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to publish this listing?'**
  String get publishListingConfirmation;

  /// No description provided for @deactivateListingConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to deactivate this listing?'**
  String get deactivateListingConfirmation;

  /// No description provided for @deactivateListing.
  ///
  /// In en, this message translates to:
  /// **'Deactivate Listing'**
  String get deactivateListing;

  /// No description provided for @changeListingStatus.
  ///
  /// In en, this message translates to:
  /// **'Change Listing Status'**
  String get changeListingStatus;

  /// No description provided for @currentStatus.
  ///
  /// In en, this message translates to:
  /// **'Current Status'**
  String get currentStatus;

  /// No description provided for @selectNewStatus.
  ///
  /// In en, this message translates to:
  /// **'Select New Status'**
  String get selectNewStatus;

  /// No description provided for @changeReason.
  ///
  /// In en, this message translates to:
  /// **'Change Reason'**
  String get changeReason;

  /// No description provided for @enterChangeReason.
  ///
  /// In en, this message translates to:
  /// **'Enter reason for status change...'**
  String get enterChangeReason;

  /// No description provided for @changeStatus.
  ///
  /// In en, this message translates to:
  /// **'Change Status'**
  String get changeStatus;

  /// No description provided for @statusChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Status changed successfully'**
  String get statusChangedSuccessfully;

  /// No description provided for @statusChangeError.
  ///
  /// In en, this message translates to:
  /// **'Error changing status'**
  String get statusChangeError;

  /// No description provided for @bulkActions.
  ///
  /// In en, this message translates to:
  /// **'Bulk Actions'**
  String get bulkActions;

  /// No description provided for @listingsSelected.
  ///
  /// In en, this message translates to:
  /// **'listings selected'**
  String get listingsSelected;

  /// No description provided for @currentStatusBreakdown.
  ///
  /// In en, this message translates to:
  /// **'Current Status Breakdown'**
  String get currentStatusBreakdown;

  /// No description provided for @selectAction.
  ///
  /// In en, this message translates to:
  /// **'Select Action'**
  String get selectAction;

  /// No description provided for @applyAction.
  ///
  /// In en, this message translates to:
  /// **'Apply Action'**
  String get applyAction;

  /// No description provided for @activateAll.
  ///
  /// In en, this message translates to:
  /// **'Activate All'**
  String get activateAll;

  /// No description provided for @deactivateAll.
  ///
  /// In en, this message translates to:
  /// **'Deactivate All'**
  String get deactivateAll;

  /// No description provided for @convertToDraft.
  ///
  /// In en, this message translates to:
  /// **'Convert to Draft'**
  String get convertToDraft;

  /// No description provided for @deleteAll.
  ///
  /// In en, this message translates to:
  /// **'Delete All'**
  String get deleteAll;

  /// No description provided for @activateAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Make all selected listings visible to guests'**
  String get activateAllDescription;

  /// No description provided for @deactivateAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Hide all selected listings from guests'**
  String get deactivateAllDescription;

  /// No description provided for @convertToDraftDescription.
  ///
  /// In en, this message translates to:
  /// **'Convert all selected listings to draft status'**
  String get convertToDraftDescription;

  /// No description provided for @deleteAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Permanently delete all selected listings'**
  String get deleteAllDescription;

  /// No description provided for @bulkActionCompleted.
  ///
  /// In en, this message translates to:
  /// **'Bulk action completed successfully'**
  String get bulkActionCompleted;

  /// No description provided for @bulkActionError.
  ///
  /// In en, this message translates to:
  /// **'Error performing bulk action'**
  String get bulkActionError;

  /// No description provided for @analyticsOverview.
  ///
  /// In en, this message translates to:
  /// **'Analytics Overview'**
  String get analyticsOverview;

  /// No description provided for @last7Days.
  ///
  /// In en, this message translates to:
  /// **'Last 7 Days'**
  String get last7Days;

  /// No description provided for @last30Days.
  ///
  /// In en, this message translates to:
  /// **'Last 30 Days'**
  String get last30Days;

  /// No description provided for @last90Days.
  ///
  /// In en, this message translates to:
  /// **'Last 90 Days'**
  String get last90Days;

  /// No description provided for @lastYear.
  ///
  /// In en, this message translates to:
  /// **'Last Year'**
  String get lastYear;

  /// No description provided for @exportData.
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportData;

  /// No description provided for @performanceGrade.
  ///
  /// In en, this message translates to:
  /// **'Performance Grade'**
  String get performanceGrade;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @charts.
  ///
  /// In en, this message translates to:
  /// **'Charts'**
  String get charts;

  /// No description provided for @insights.
  ///
  /// In en, this message translates to:
  /// **'Insights'**
  String get insights;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @responseTime.
  ///
  /// In en, this message translates to:
  /// **'Response Time'**
  String get responseTime;

  /// No description provided for @responseRate.
  ///
  /// In en, this message translates to:
  /// **'Response Rate'**
  String get responseRate;

  /// No description provided for @viewsTrend.
  ///
  /// In en, this message translates to:
  /// **'Views Trend'**
  String get viewsTrend;

  /// No description provided for @dailyViews.
  ///
  /// In en, this message translates to:
  /// **'Daily Views'**
  String get dailyViews;

  /// No description provided for @dailyBookings.
  ///
  /// In en, this message translates to:
  /// **'Daily Bookings'**
  String get dailyBookings;

  /// No description provided for @dailyRevenue.
  ///
  /// In en, this message translates to:
  /// **'Daily Revenue'**
  String get dailyRevenue;

  /// No description provided for @bookingMetrics.
  ///
  /// In en, this message translates to:
  /// **'Booking Metrics'**
  String get bookingMetrics;

  /// No description provided for @cancelledBookings.
  ///
  /// In en, this message translates to:
  /// **'Cancelled Bookings'**
  String get cancelledBookings;

  /// No description provided for @cancellationRate.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Rate'**
  String get cancellationRate;

  /// No description provided for @revenueMetrics.
  ///
  /// In en, this message translates to:
  /// **'Revenue Metrics'**
  String get revenueMetrics;

  /// No description provided for @netRevenue.
  ///
  /// In en, this message translates to:
  /// **'Net Revenue'**
  String get netRevenue;

  /// No description provided for @averageDailyRate.
  ///
  /// In en, this message translates to:
  /// **'Average Daily Rate'**
  String get averageDailyRate;

  /// No description provided for @engagementMetrics.
  ///
  /// In en, this message translates to:
  /// **'Engagement Metrics'**
  String get engagementMetrics;

  /// No description provided for @uniqueViews.
  ///
  /// In en, this message translates to:
  /// **'Unique Views'**
  String get uniqueViews;

  /// No description provided for @favoriteCount.
  ///
  /// In en, this message translates to:
  /// **'Favorites'**
  String get favoriteCount;

  /// No description provided for @shareCount.
  ///
  /// In en, this message translates to:
  /// **'Shares'**
  String get shareCount;

  /// No description provided for @analytics.
  ///
  /// In en, this message translates to:
  /// **'Analytics'**
  String get analytics;

  /// No description provided for @advancedBulkActions.
  ///
  /// In en, this message translates to:
  /// **'Advanced Bulk Actions'**
  String get advancedBulkActions;

  /// No description provided for @totalValue.
  ///
  /// In en, this message translates to:
  /// **'Total Value'**
  String get totalValue;

  /// No description provided for @selectActionCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Action Category'**
  String get selectActionCategory;

  /// No description provided for @statusActions.
  ///
  /// In en, this message translates to:
  /// **'Status Actions'**
  String get statusActions;

  /// No description provided for @pricingActions.
  ///
  /// In en, this message translates to:
  /// **'Pricing Actions'**
  String get pricingActions;

  /// No description provided for @managementActions.
  ///
  /// In en, this message translates to:
  /// **'Management Actions'**
  String get managementActions;

  /// No description provided for @executeAction.
  ///
  /// In en, this message translates to:
  /// **'Execute Action'**
  String get executeAction;

  /// No description provided for @actionParameters.
  ///
  /// In en, this message translates to:
  /// **'Action Parameters'**
  String get actionParameters;

  /// No description provided for @percentage.
  ///
  /// In en, this message translates to:
  /// **'Percentage'**
  String get percentage;

  /// No description provided for @newPrice.
  ///
  /// In en, this message translates to:
  /// **'New Price'**
  String get newPrice;

  /// No description provided for @discountPercentage.
  ///
  /// In en, this message translates to:
  /// **'Discount Percentage'**
  String get discountPercentage;

  /// No description provided for @discountDuration.
  ///
  /// In en, this message translates to:
  /// **'Discount Duration'**
  String get discountDuration;

  /// No description provided for @publishAll.
  ///
  /// In en, this message translates to:
  /// **'Publish All'**
  String get publishAll;

  /// No description provided for @increasePrices.
  ///
  /// In en, this message translates to:
  /// **'Increase Prices'**
  String get increasePrices;

  /// No description provided for @decreasePrices.
  ///
  /// In en, this message translates to:
  /// **'Decrease Prices'**
  String get decreasePrices;

  /// No description provided for @setPrices.
  ///
  /// In en, this message translates to:
  /// **'Set Prices'**
  String get setPrices;

  /// No description provided for @applyDiscount.
  ///
  /// In en, this message translates to:
  /// **'Apply Discount'**
  String get applyDiscount;

  /// No description provided for @duplicateAll.
  ///
  /// In en, this message translates to:
  /// **'Duplicate All'**
  String get duplicateAll;

  /// No description provided for @exportAll.
  ///
  /// In en, this message translates to:
  /// **'Export All'**
  String get exportAll;

  /// No description provided for @archiveAll.
  ///
  /// In en, this message translates to:
  /// **'Archive All'**
  String get archiveAll;

  /// No description provided for @publishAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Publish all selected listings'**
  String get publishAllDescription;

  /// No description provided for @increasePricesDescription.
  ///
  /// In en, this message translates to:
  /// **'Increase prices by percentage'**
  String get increasePricesDescription;

  /// No description provided for @decreasePricesDescription.
  ///
  /// In en, this message translates to:
  /// **'Decrease prices by percentage'**
  String get decreasePricesDescription;

  /// No description provided for @setPricesDescription.
  ///
  /// In en, this message translates to:
  /// **'Set fixed price for all listings'**
  String get setPricesDescription;

  /// No description provided for @applyDiscountDescription.
  ///
  /// In en, this message translates to:
  /// **'Apply temporary discount'**
  String get applyDiscountDescription;

  /// No description provided for @duplicateAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Create copies of selected listings'**
  String get duplicateAllDescription;

  /// No description provided for @exportAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Export listing data'**
  String get exportAllDescription;

  /// No description provided for @archiveAllDescription.
  ///
  /// In en, this message translates to:
  /// **'Archive selected listings'**
  String get archiveAllDescription;

  /// No description provided for @itemsSelected.
  ///
  /// In en, this message translates to:
  /// **'items selected'**
  String get itemsSelected;

  /// No description provided for @clearSelection.
  ///
  /// In en, this message translates to:
  /// **'Clear Selection'**
  String get clearSelection;

  /// No description provided for @selectAll.
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// No description provided for @moreActions.
  ///
  /// In en, this message translates to:
  /// **'More Actions'**
  String get moreActions;

  /// No description provided for @activateSelected.
  ///
  /// In en, this message translates to:
  /// **'Activate Selected'**
  String get activateSelected;

  /// No description provided for @deactivateSelected.
  ///
  /// In en, this message translates to:
  /// **'Deactivate Selected'**
  String get deactivateSelected;

  /// No description provided for @deleteSelected.
  ///
  /// In en, this message translates to:
  /// **'Delete Selected'**
  String get deleteSelected;

  /// No description provided for @activateSelectedConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to activate the selected listings?'**
  String get activateSelectedConfirmation;

  /// No description provided for @deactivateSelectedConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to deactivate the selected listings?'**
  String get deactivateSelectedConfirmation;

  /// No description provided for @deleteSelectedConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the selected listings? This action cannot be undone.'**
  String get deleteSelectedConfirmation;

  /// No description provided for @listingsWillBeAffected.
  ///
  /// In en, this message translates to:
  /// **'listings will be affected'**
  String get listingsWillBeAffected;

  /// No description provided for @loadingPropertyData.
  ///
  /// In en, this message translates to:
  /// **'Loading property data...'**
  String get loadingPropertyData;

  /// No description provided for @errorLoadingProperty.
  ///
  /// In en, this message translates to:
  /// **'Error Loading Property'**
  String get errorLoadingProperty;

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// No description provided for @propertyNotFound.
  ///
  /// In en, this message translates to:
  /// **'Property Not Found'**
  String get propertyNotFound;

  /// No description provided for @propertyNotFoundDescription.
  ///
  /// In en, this message translates to:
  /// **'The property you\'re looking for doesn\'t exist or has been removed.'**
  String get propertyNotFoundDescription;

  /// No description provided for @createNewProperty.
  ///
  /// In en, this message translates to:
  /// **'Create New Property'**
  String get createNewProperty;

  /// No description provided for @propertyImages.
  ///
  /// In en, this message translates to:
  /// **'Property Images'**
  String get propertyImages;

  /// No description provided for @pleaseEnterTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter property title'**
  String get pleaseEnterTitle;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @saveAsDraft.
  ///
  /// In en, this message translates to:
  /// **'Save as Draft'**
  String get saveAsDraft;

  /// No description provided for @updateProperty.
  ///
  /// In en, this message translates to:
  /// **'Update Property'**
  String get updateProperty;

  /// No description provided for @propertyUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Property updated successfully'**
  String get propertyUpdatedSuccessfully;

  /// No description provided for @propertyTitleRequired.
  ///
  /// In en, this message translates to:
  /// **'Property title is required'**
  String get propertyTitleRequired;

  /// No description provided for @propertyTitleTooShort.
  ///
  /// In en, this message translates to:
  /// **'Property title must be at least 3 characters'**
  String get propertyTitleTooShort;

  /// No description provided for @propertyDescriptionRequired.
  ///
  /// In en, this message translates to:
  /// **'Property description is required'**
  String get propertyDescriptionRequired;

  /// No description provided for @propertyDescriptionTooShort.
  ///
  /// In en, this message translates to:
  /// **'Property description must be at least 10 characters'**
  String get propertyDescriptionTooShort;

  /// No description provided for @priceRequired.
  ///
  /// In en, this message translates to:
  /// **'Price is required'**
  String get priceRequired;

  /// No description provided for @priceInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid price'**
  String get priceInvalid;

  /// No description provided for @priceMinimum.
  ///
  /// In en, this message translates to:
  /// **'Minimum price is 50 SAR per night'**
  String get priceMinimum;

  /// No description provided for @categoryRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a category'**
  String get categoryRequired;

  /// No description provided for @propertyTypeRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a property type'**
  String get propertyTypeRequired;

  /// No description provided for @cancellationPolicyRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a cancellation policy'**
  String get cancellationPolicyRequired;

  /// No description provided for @guestsRequired.
  ///
  /// In en, this message translates to:
  /// **'Number of guests is required'**
  String get guestsRequired;

  /// No description provided for @guestsInvalid.
  ///
  /// In en, this message translates to:
  /// **'Number of guests must be between 1 and 20'**
  String get guestsInvalid;

  /// No description provided for @bedsRequired.
  ///
  /// In en, this message translates to:
  /// **'Number of bedrooms is required'**
  String get bedsRequired;

  /// No description provided for @bedsInvalid.
  ///
  /// In en, this message translates to:
  /// **'Number of bedrooms must be between 1 and 10'**
  String get bedsInvalid;

  /// No description provided for @bathsRequired.
  ///
  /// In en, this message translates to:
  /// **'Number of bathrooms is required'**
  String get bathsRequired;

  /// No description provided for @bathsInvalid.
  ///
  /// In en, this message translates to:
  /// **'Number of bathrooms must be between 1 and 10'**
  String get bathsInvalid;

  /// No description provided for @facilitiesRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select at least one facility'**
  String get facilitiesRequired;

  /// No description provided for @locationRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a location'**
  String get locationRequired;

  /// No description provided for @imagesRequired.
  ///
  /// In en, this message translates to:
  /// **'Please add at least one image'**
  String get imagesRequired;

  /// No description provided for @imagesMinimum.
  ///
  /// In en, this message translates to:
  /// **'Please add at least 3 images'**
  String get imagesMinimum;

  /// No description provided for @categoryAndType.
  ///
  /// In en, this message translates to:
  /// **'Category & Type'**
  String get categoryAndType;

  /// No description provided for @locationAndAddress.
  ///
  /// In en, this message translates to:
  /// **'Location & Address'**
  String get locationAndAddress;

  /// No description provided for @photosAndVideo.
  ///
  /// In en, this message translates to:
  /// **'Photos & Video'**
  String get photosAndVideo;

  /// No description provided for @reviewAndSubmit.
  ///
  /// In en, this message translates to:
  /// **'Review & Submit'**
  String get reviewAndSubmit;

  /// No description provided for @savingProperty.
  ///
  /// In en, this message translates to:
  /// **'Saving property...'**
  String get savingProperty;

  /// No description provided for @validationFailed.
  ///
  /// In en, this message translates to:
  /// **'Please fix the errors and try again'**
  String get validationFailed;

  /// No description provided for @basicInformationDesc.
  ///
  /// In en, this message translates to:
  /// **'Tell us about your property'**
  String get basicInformationDesc;

  /// No description provided for @propertyTitleHint.
  ///
  /// In en, this message translates to:
  /// **'Enter a catchy title for your property'**
  String get propertyTitleHint;

  /// No description provided for @propertyDescriptionHint.
  ///
  /// In en, this message translates to:
  /// **'Describe your property in detail'**
  String get propertyDescriptionHint;

  /// No description provided for @priceHint.
  ///
  /// In en, this message translates to:
  /// **'100'**
  String get priceHint;

  /// No description provided for @priceGuidance.
  ///
  /// In en, this message translates to:
  /// **'Tip: Research similar properties in your area to set competitive pricing'**
  String get priceGuidance;

  /// No description provided for @locationSelected.
  ///
  /// In en, this message translates to:
  /// **'Location Selected'**
  String get locationSelected;

  /// No description provided for @changeLocation.
  ///
  /// In en, this message translates to:
  /// **'Change Location'**
  String get changeLocation;

  /// No description provided for @propertyPhotos.
  ///
  /// In en, this message translates to:
  /// **'Property Photos'**
  String get propertyPhotos;

  /// No description provided for @propertyVideoOptional.
  ///
  /// In en, this message translates to:
  /// **'Property Video (Optional)'**
  String get propertyVideoOptional;

  /// No description provided for @addPhotos.
  ///
  /// In en, this message translates to:
  /// **'Add Photos'**
  String get addPhotos;

  /// No description provided for @addVideo.
  ///
  /// In en, this message translates to:
  /// **'Add Video'**
  String get addVideo;

  /// No description provided for @changeVideo.
  ///
  /// In en, this message translates to:
  /// **'Change Video'**
  String get changeVideo;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @chooseFromGallery.
  ///
  /// In en, this message translates to:
  /// **'Choose from Gallery'**
  String get chooseFromGallery;

  /// No description provided for @chooseMultiple.
  ///
  /// In en, this message translates to:
  /// **'Choose Multiple'**
  String get chooseMultiple;

  /// No description provided for @noPhotosAdded.
  ///
  /// In en, this message translates to:
  /// **'No photos added yet'**
  String get noPhotosAdded;

  /// No description provided for @videoPreview.
  ///
  /// In en, this message translates to:
  /// **'Video Preview'**
  String get videoPreview;

  /// No description provided for @main.
  ///
  /// In en, this message translates to:
  /// **'Main'**
  String get main;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @notProvided.
  ///
  /// In en, this message translates to:
  /// **'Not provided'**
  String get notProvided;

  /// No description provided for @creatingProperty.
  ///
  /// In en, this message translates to:
  /// **'Creating your property...'**
  String get creatingProperty;

  /// No description provided for @pleaseWaitProcessing.
  ///
  /// In en, this message translates to:
  /// **'Please wait while we process your information'**
  String get pleaseWaitProcessing;

  /// No description provided for @categoryTypeDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose the category and type that best describes your property'**
  String get categoryTypeDescription;

  /// No description provided for @bookingRulesDescription.
  ///
  /// In en, this message translates to:
  /// **'Set booking rules and provide tourism permit information'**
  String get bookingRulesDescription;

  /// No description provided for @bookingRulesHint.
  ///
  /// In en, this message translates to:
  /// **'Enter any specific rules for booking your property (optional)'**
  String get bookingRulesHint;

  /// No description provided for @tourismPermitNumberHint.
  ///
  /// In en, this message translates to:
  /// **'Enter your tourism permit number (required)'**
  String get tourismPermitNumberHint;

  /// No description provided for @tourismPermitDocument.
  ///
  /// In en, this message translates to:
  /// **'Tourism Permit Document *'**
  String get tourismPermitDocument;

  /// No description provided for @tourismPermitDocumentHint.
  ///
  /// In en, this message translates to:
  /// **'Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)'**
  String get tourismPermitDocumentHint;

  /// No description provided for @documentSelected.
  ///
  /// In en, this message translates to:
  /// **'Document Selected'**
  String get documentSelected;

  /// No description provided for @changeDocument.
  ///
  /// In en, this message translates to:
  /// **'Change Document'**
  String get changeDocument;

  /// No description provided for @uploadDocument.
  ///
  /// In en, this message translates to:
  /// **'Upload Document'**
  String get uploadDocument;

  /// No description provided for @tourismPermitInfo.
  ///
  /// In en, this message translates to:
  /// **'Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations.'**
  String get tourismPermitInfo;

  /// No description provided for @propertyDetailsDescription.
  ///
  /// In en, this message translates to:
  /// **'Specify the details and amenities of your property'**
  String get propertyDetailsDescription;

  /// No description provided for @tourismPermitNumberRequired.
  ///
  /// In en, this message translates to:
  /// **'Tourism permit number is required'**
  String get tourismPermitNumberRequired;

  /// No description provided for @tourismPermitNumberMinLength.
  ///
  /// In en, this message translates to:
  /// **'Tourism permit number should be at least 5 characters'**
  String get tourismPermitNumberMinLength;

  /// No description provided for @tourismPermitDocumentRequired.
  ///
  /// In en, this message translates to:
  /// **'Tourism permit document is required'**
  String get tourismPermitDocumentRequired;

  /// No description provided for @bookingRulesMinLength.
  ///
  /// In en, this message translates to:
  /// **'Booking rules should be at least 10 characters if provided'**
  String get bookingRulesMinLength;

  /// No description provided for @reviewDetails.
  ///
  /// In en, this message translates to:
  /// **'Review Details'**
  String get reviewDetails;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @bookingRulesReview.
  ///
  /// In en, this message translates to:
  /// **'Booking Rules'**
  String get bookingRulesReview;

  /// No description provided for @tourismPermitNumberReview.
  ///
  /// In en, this message translates to:
  /// **'Tourism Permit Number'**
  String get tourismPermitNumberReview;

  /// No description provided for @tourismPermitDocumentReview.
  ///
  /// In en, this message translates to:
  /// **'Tourism Permit Document'**
  String get tourismPermitDocumentReview;

  /// No description provided for @photos.
  ///
  /// In en, this message translates to:
  /// **'Photos'**
  String get photos;

  /// No description provided for @notSelected.
  ///
  /// In en, this message translates to:
  /// **'Not selected'**
  String get notSelected;

  /// No description provided for @noneSelected.
  ///
  /// In en, this message translates to:
  /// **'None selected'**
  String get noneSelected;

  /// No description provided for @unknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// No description provided for @selected.
  ///
  /// In en, this message translates to:
  /// **'Selected'**
  String get selected;

  /// No description provided for @uploaded.
  ///
  /// In en, this message translates to:
  /// **'Uploaded'**
  String get uploaded;

  /// No description provided for @notUploaded.
  ///
  /// In en, this message translates to:
  /// **'Not uploaded'**
  String get notUploaded;

  /// No description provided for @added.
  ///
  /// In en, this message translates to:
  /// **'Added'**
  String get added;

  /// No description provided for @notAdded.
  ///
  /// In en, this message translates to:
  /// **'Not added'**
  String get notAdded;

  /// No description provided for @images.
  ///
  /// In en, this message translates to:
  /// **'images'**
  String get images;

  /// No description provided for @addPhoto.
  ///
  /// In en, this message translates to:
  /// **'Add Photo'**
  String get addPhoto;

  /// No description provided for @selectFacilities.
  ///
  /// In en, this message translates to:
  /// **'Select Facilities'**
  String get selectFacilities;

  /// No description provided for @failedToLoadPropertyTypes.
  ///
  /// In en, this message translates to:
  /// **'Failed to load property types'**
  String get failedToLoadPropertyTypes;

  /// No description provided for @failedToLoadCancellationPolicies.
  ///
  /// In en, this message translates to:
  /// **'Failed to load cancellation policies'**
  String get failedToLoadCancellationPolicies;

  /// No description provided for @loadingCategories.
  ///
  /// In en, this message translates to:
  /// **'Loading categories...'**
  String get loadingCategories;

  /// No description provided for @loadingPropertyTypes.
  ///
  /// In en, this message translates to:
  /// **'Loading property types...'**
  String get loadingPropertyTypes;

  /// No description provided for @loadingCancellationPolicies.
  ///
  /// In en, this message translates to:
  /// **'Loading cancellation policies...'**
  String get loadingCancellationPolicies;

  /// No description provided for @loadingFacilities.
  ///
  /// In en, this message translates to:
  /// **'Loading facilities...'**
  String get loadingFacilities;

  /// No description provided for @propertyTypeOption.
  ///
  /// In en, this message translates to:
  /// **'Property type option'**
  String get propertyTypeOption;

  /// No description provided for @friends.
  ///
  /// In en, this message translates to:
  /// **'Friends'**
  String get friends;

  /// No description provided for @requests.
  ///
  /// In en, this message translates to:
  /// **'Requests'**
  String get requests;

  /// No description provided for @searchFriends.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get searchFriends;

  /// No description provided for @searchFriendsHint.
  ///
  /// In en, this message translates to:
  /// **'Search for friends by name or email'**
  String get searchFriendsHint;

  /// No description provided for @searchForFriends.
  ///
  /// In en, this message translates to:
  /// **'Search for friends'**
  String get searchForFriends;

  /// No description provided for @searchForFriendsDescription.
  ///
  /// In en, this message translates to:
  /// **'Type a name or email to search for new friends'**
  String get searchForFriendsDescription;

  /// No description provided for @noFriendsYet.
  ///
  /// In en, this message translates to:
  /// **'No friends yet'**
  String get noFriendsYet;

  /// No description provided for @noFriendsDescription.
  ///
  /// In en, this message translates to:
  /// **'Start by adding new friends to connect with them'**
  String get noFriendsDescription;

  /// No description provided for @noPendingRequests.
  ///
  /// In en, this message translates to:
  /// **'No pending requests'**
  String get noPendingRequests;

  /// No description provided for @noPendingRequestsDescription.
  ///
  /// In en, this message translates to:
  /// **'Incoming friend requests will appear here'**
  String get noPendingRequestsDescription;

  /// No description provided for @noSearchResultsDescription.
  ///
  /// In en, this message translates to:
  /// **'No users found with this name'**
  String get noSearchResultsDescription;

  /// No description provided for @addFriend.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get addFriend;

  /// No description provided for @acceptRequest.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get acceptRequest;

  /// No description provided for @declineRequest.
  ///
  /// In en, this message translates to:
  /// **'Decline'**
  String get declineRequest;

  /// No description provided for @removeFriend.
  ///
  /// In en, this message translates to:
  /// **'Remove friend'**
  String get removeFriend;

  /// No description provided for @alreadyFriends.
  ///
  /// In en, this message translates to:
  /// **'Friends'**
  String get alreadyFriends;

  /// No description provided for @requestSent.
  ///
  /// In en, this message translates to:
  /// **'Sent'**
  String get requestSent;

  /// No description provided for @host.
  ///
  /// In en, this message translates to:
  /// **'Host'**
  String get host;

  /// No description provided for @mutualFriends.
  ///
  /// In en, this message translates to:
  /// **'mutual friends'**
  String get mutualFriends;

  /// No description provided for @friendRequestSentSuccess.
  ///
  /// In en, this message translates to:
  /// **'Friend request sent successfully'**
  String get friendRequestSentSuccess;

  /// No description provided for @friendRequestSentError.
  ///
  /// In en, this message translates to:
  /// **'Error sending friend request'**
  String get friendRequestSentError;

  /// No description provided for @friendRequestAcceptedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Friend request accepted successfully'**
  String get friendRequestAcceptedSuccess;

  /// No description provided for @friendRequestAcceptedError.
  ///
  /// In en, this message translates to:
  /// **'Error accepting friend request'**
  String get friendRequestAcceptedError;

  /// No description provided for @friendRequestDeclinedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Friend request declined successfully'**
  String get friendRequestDeclinedSuccess;

  /// No description provided for @friendRequestDeclinedError.
  ///
  /// In en, this message translates to:
  /// **'Error declining friend request'**
  String get friendRequestDeclinedError;

  /// No description provided for @friendRemovedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Friend removed successfully'**
  String get friendRemovedSuccess;

  /// No description provided for @friendRemovedError.
  ///
  /// In en, this message translates to:
  /// **'Error removing friend'**
  String get friendRemovedError;

  /// No description provided for @searchError.
  ///
  /// In en, this message translates to:
  /// **'Search error'**
  String get searchError;

  /// No description provided for @loadingFriends.
  ///
  /// In en, this message translates to:
  /// **'Loading friends...'**
  String get loadingFriends;

  /// No description provided for @loadingRequests.
  ///
  /// In en, this message translates to:
  /// **'Loading requests...'**
  String get loadingRequests;

  /// No description provided for @refreshFriends.
  ///
  /// In en, this message translates to:
  /// **'Refresh friends list'**
  String get refreshFriends;

  /// No description provided for @goToPendingRequests.
  ///
  /// In en, this message translates to:
  /// **'Please go to the requests tab to accept the friend request'**
  String get goToPendingRequests;

  /// No description provided for @messageFeatureInDevelopment.
  ///
  /// In en, this message translates to:
  /// **'Messaging feature is under development'**
  String get messageFeatureInDevelopment;

  /// No description provided for @failedToLoadFriends.
  ///
  /// In en, this message translates to:
  /// **'Failed to load friends'**
  String get failedToLoadFriends;

  /// No description provided for @failedToLoadRequests.
  ///
  /// In en, this message translates to:
  /// **'Failed to load requests'**
  String get failedToLoadRequests;

  /// No description provided for @acquaintances.
  ///
  /// In en, this message translates to:
  /// **'Acquaintances'**
  String get acquaintances;

  /// No description provided for @sendMessage.
  ///
  /// In en, this message translates to:
  /// **'Send message'**
  String get sendMessage;

  /// No description provided for @friendRequestTime.
  ///
  /// In en, this message translates to:
  /// **'Friend request'**
  String get friendRequestTime;

  /// No description provided for @retryButton.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retryButton;

  /// No description provided for @minutesAgo.
  ///
  /// In en, this message translates to:
  /// **'minutes ago'**
  String get minutesAgo;

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **'hours ago'**
  String get hoursAgo;

  /// No description provided for @daysAgo.
  ///
  /// In en, this message translates to:
  /// **'days ago'**
  String get daysAgo;

  /// No description provided for @ago.
  ///
  /// In en, this message translates to:
  /// **'ago'**
  String get ago;

  /// No description provided for @hostModeActivated.
  ///
  /// In en, this message translates to:
  /// **'Host mode activated successfully'**
  String get hostModeActivated;

  /// No description provided for @hostModeDeactivated.
  ///
  /// In en, this message translates to:
  /// **'Host mode deactivated successfully'**
  String get hostModeDeactivated;

  /// No description provided for @earlyAccessFeatures.
  ///
  /// In en, this message translates to:
  /// **'Early Access Features'**
  String get earlyAccessFeatures;

  /// No description provided for @newLabel.
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get newLabel;

  /// No description provided for @guestUser.
  ///
  /// In en, this message translates to:
  /// **'Guest User'**
  String get guestUser;

  /// No description provided for @loginForFullExperience.
  ///
  /// In en, this message translates to:
  /// **'Login for full experience'**
  String get loginForFullExperience;

  /// No description provided for @jeddahSaudiArabia.
  ///
  /// In en, this message translates to:
  /// **'Jeddah, Saudi Arabia'**
  String get jeddahSaudiArabia;

  /// No description provided for @loginRequired.
  ///
  /// In en, this message translates to:
  /// **'Login Required'**
  String get loginRequired;

  /// No description provided for @loginRequiredMessage.
  ///
  /// In en, this message translates to:
  /// **'You must login to access this feature'**
  String get loginRequiredMessage;

  /// No description provided for @switchToTravel.
  ///
  /// In en, this message translates to:
  /// **'Switch to Travel'**
  String get switchToTravel;

  /// No description provided for @hosting.
  ///
  /// In en, this message translates to:
  /// **'Hosting'**
  String get hosting;

  /// No description provided for @editProfileTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfileTitle;

  /// No description provided for @saveChangesTooltip.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChangesTooltip;

  /// No description provided for @unexpectedError.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred'**
  String get unexpectedError;

  /// No description provided for @pleaseSelectBirthdate.
  ///
  /// In en, this message translates to:
  /// **'Please select birthdate'**
  String get pleaseSelectBirthdate;

  /// No description provided for @validationError.
  ///
  /// In en, this message translates to:
  /// **'Validation error'**
  String get validationError;

  /// No description provided for @accountSettingsTitle.
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get accountSettingsTitle;

  /// No description provided for @loginRequiredForSettings.
  ///
  /// In en, this message translates to:
  /// **'You must login to access account settings'**
  String get loginRequiredForSettings;

  /// No description provided for @accountInfo.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInfo;

  /// No description provided for @editProfileSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Update name, photo and personal information'**
  String get editProfileSubtitle;

  /// No description provided for @emailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// No description provided for @phoneNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberLabel;

  /// No description provided for @security.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get security;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @changePasswordSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Update your password'**
  String get changePasswordSubtitle;

  /// No description provided for @twoFactorAuth.
  ///
  /// In en, this message translates to:
  /// **'Two-Factor Authentication'**
  String get twoFactorAuth;

  /// No description provided for @twoFactorAuthSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Additional security for your account'**
  String get twoFactorAuthSubtitle;

  /// No description provided for @connectedDevices.
  ///
  /// In en, this message translates to:
  /// **'Connected Devices'**
  String get connectedDevices;

  /// No description provided for @connectedDevicesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Manage devices you\'re logged in from'**
  String get connectedDevicesSubtitle;

  /// No description provided for @preferences.
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// No description provided for @notificationsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Manage notification settings'**
  String get notificationsSubtitle;

  /// No description provided for @locationSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Privacy and location settings'**
  String get locationSubtitle;

  /// No description provided for @dangerZone.
  ///
  /// In en, this message translates to:
  /// **'Danger Zone'**
  String get dangerZone;

  /// No description provided for @deleteAccountSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Permanently delete your account - cannot be undone'**
  String get deleteAccountSubtitle;

  /// No description provided for @deleteAccountTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccountTitle;

  /// No description provided for @deleteAccountConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete your account? This action cannot be undone and all your data will be lost.'**
  String get deleteAccountConfirmMessage;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @supportCenter.
  ///
  /// In en, this message translates to:
  /// **'Support Center'**
  String get supportCenter;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get errorOccurred;

  /// No description provided for @frequentlyAskedQuestions.
  ///
  /// In en, this message translates to:
  /// **'Frequently Asked Questions'**
  String get frequentlyAskedQuestions;

  /// No description provided for @supportTickets.
  ///
  /// In en, this message translates to:
  /// **'Support Tickets'**
  String get supportTickets;

  /// No description provided for @newTicket.
  ///
  /// In en, this message translates to:
  /// **'New Ticket'**
  String get newTicket;

  /// No description provided for @noFaqsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No FAQs available'**
  String get noFaqsAvailable;

  /// No description provided for @noSupportTickets.
  ///
  /// In en, this message translates to:
  /// **'No support tickets'**
  String get noSupportTickets;

  /// No description provided for @ticketCreatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Ticket created successfully'**
  String get ticketCreatedSuccessfully;

  /// No description provided for @createNewSupportTicket.
  ///
  /// In en, this message translates to:
  /// **'Create New Support Ticket'**
  String get createNewSupportTicket;

  /// No description provided for @subject.
  ///
  /// In en, this message translates to:
  /// **'Subject'**
  String get subject;

  /// No description provided for @pleaseEnterSubject.
  ///
  /// In en, this message translates to:
  /// **'Please enter subject'**
  String get pleaseEnterSubject;

  /// No description provided for @descriptionLabel.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get descriptionLabel;

  /// No description provided for @priority.
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// No description provided for @low.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// No description provided for @medium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// No description provided for @high.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// No description provided for @urgent.
  ///
  /// In en, this message translates to:
  /// **'Urgent'**
  String get urgent;

  /// No description provided for @submitTicket.
  ///
  /// In en, this message translates to:
  /// **'Submit Ticket'**
  String get submitTicket;

  /// No description provided for @helpAndSupport.
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpAndSupport;

  /// No description provided for @quickHelp.
  ///
  /// In en, this message translates to:
  /// **'Quick Help'**
  String get quickHelp;

  /// No description provided for @howToSearch.
  ///
  /// In en, this message translates to:
  /// **'How to Search'**
  String get howToSearch;

  /// No description provided for @makeBooking.
  ///
  /// In en, this message translates to:
  /// **'Make a Booking'**
  String get makeBooking;

  /// No description provided for @paymentAndBilling.
  ///
  /// In en, this message translates to:
  /// **'Payment & Billing'**
  String get paymentAndBilling;

  /// No description provided for @frequentQuestions.
  ///
  /// In en, this message translates to:
  /// **'Frequently Asked Questions'**
  String get frequentQuestions;

  /// No description provided for @howToCancelBooking.
  ///
  /// In en, this message translates to:
  /// **'How can I cancel a booking?'**
  String get howToCancelBooking;

  /// No description provided for @howToCancelBookingAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can cancel the booking by going to \"My Bookings\" and selecting the booking you want to cancel.'**
  String get howToCancelBookingAnswer;

  /// No description provided for @refundPolicy.
  ///
  /// In en, this message translates to:
  /// **'What is the refund policy?'**
  String get refundPolicy;

  /// No description provided for @refundPolicyAnswer.
  ///
  /// In en, this message translates to:
  /// **'Refund policy varies by property type and host policy. You can review the details on the booking page.'**
  String get refundPolicyAnswer;

  /// No description provided for @howToChangeBooking.
  ///
  /// In en, this message translates to:
  /// **'How can I change booking information?'**
  String get howToChangeBooking;

  /// No description provided for @howToChangeBookingAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can modify some booking information by contacting the host or customer service.'**
  String get howToChangeBookingAnswer;

  /// No description provided for @howToBecomeHost.
  ///
  /// In en, this message translates to:
  /// **'How do I become a host?'**
  String get howToBecomeHost;

  /// No description provided for @howToBecomeHostAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can switch to host mode from your profile and add your first property.'**
  String get howToBecomeHostAnswer;

  /// No description provided for @liveChat.
  ///
  /// In en, this message translates to:
  /// **'Live Chat'**
  String get liveChat;

  /// No description provided for @available24_7.
  ///
  /// In en, this message translates to:
  /// **'Available 24/7'**
  String get available24_7;

  /// No description provided for @emailSupport.
  ///
  /// In en, this message translates to:
  /// **'Email Support'**
  String get emailSupport;

  /// No description provided for @phoneSupport.
  ///
  /// In en, this message translates to:
  /// **'Phone Support'**
  String get phoneSupport;

  /// No description provided for @usefulResources.
  ///
  /// In en, this message translates to:
  /// **'Useful Resources'**
  String get usefulResources;

  /// No description provided for @userGuide.
  ///
  /// In en, this message translates to:
  /// **'User Guide'**
  String get userGuide;

  /// No description provided for @tutorialVideos.
  ///
  /// In en, this message translates to:
  /// **'Tutorial Videos'**
  String get tutorialVideos;

  /// No description provided for @helpCenter.
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenter;

  /// No description provided for @viewProfileTitle.
  ///
  /// In en, this message translates to:
  /// **'View Profile'**
  String get viewProfileTitle;

  /// No description provided for @loginRequiredToViewProfile.
  ///
  /// In en, this message translates to:
  /// **'Login required to view profile'**
  String get loginRequiredToViewProfile;

  /// No description provided for @menu.
  ///
  /// In en, this message translates to:
  /// **'Menu'**
  String get menu;

  /// No description provided for @switchToTravelMode.
  ///
  /// In en, this message translates to:
  /// **'Switch to Travel'**
  String get switchToTravelMode;

  /// No description provided for @trips.
  ///
  /// In en, this message translates to:
  /// **'Trips'**
  String get trips;

  /// No description provided for @aboutMe.
  ///
  /// In en, this message translates to:
  /// **'About Me'**
  String get aboutMe;

  /// No description provided for @noAboutMeAdded.
  ///
  /// In en, this message translates to:
  /// **'No personal bio added yet.'**
  String get noAboutMeAdded;

  /// No description provided for @languages.
  ///
  /// In en, this message translates to:
  /// **'Languages'**
  String get languages;

  /// No description provided for @arabicEnglish.
  ///
  /// In en, this message translates to:
  /// **'Arabic, English'**
  String get arabicEnglish;

  /// No description provided for @locationLabel.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get locationLabel;

  /// No description provided for @memberSince.
  ///
  /// In en, this message translates to:
  /// **'Member since'**
  String get memberSince;

  /// No description provided for @january2023.
  ///
  /// In en, this message translates to:
  /// **'January 2023'**
  String get january2023;

  /// No description provided for @verification.
  ///
  /// In en, this message translates to:
  /// **'Verification'**
  String get verification;

  /// No description provided for @emailVerification.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailVerification;

  /// No description provided for @phoneVerification.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneVerification;

  /// No description provided for @identityVerification.
  ///
  /// In en, this message translates to:
  /// **'Identity'**
  String get identityVerification;

  /// No description provided for @manageNotificationSettings.
  ///
  /// In en, this message translates to:
  /// **'Manage notification settings'**
  String get manageNotificationSettings;

  /// No description provided for @privacyLocationSettings.
  ///
  /// In en, this message translates to:
  /// **'Privacy and location settings'**
  String get privacyLocationSettings;

  /// No description provided for @faq.
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get faq;

  /// No description provided for @errorMessage.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get errorMessage;

  /// No description provided for @cancelBookingAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can cancel your booking by going to \"My Bookings\" and selecting the booking you want to cancel.'**
  String get cancelBookingAnswer;

  /// No description provided for @changeBookingInfo.
  ///
  /// In en, this message translates to:
  /// **'How can I change booking information?'**
  String get changeBookingInfo;

  /// No description provided for @changeBookingAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can modify some booking information by contacting the host or customer service.'**
  String get changeBookingAnswer;

  /// No description provided for @becomeHost.
  ///
  /// In en, this message translates to:
  /// **'How do I become a host?'**
  String get becomeHost;

  /// No description provided for @becomeHostAnswer.
  ///
  /// In en, this message translates to:
  /// **'You can switch to host mode from your profile and add your first property.'**
  String get becomeHostAnswer;

  /// No description provided for @available247.
  ///
  /// In en, this message translates to:
  /// **'Available 24/7'**
  String get available247;

  /// No description provided for @jeddahSaudi.
  ///
  /// In en, this message translates to:
  /// **'Jeddah, Saudi Arabia'**
  String get jeddahSaudi;

  /// No description provided for @noBioAdded.
  ///
  /// In en, this message translates to:
  /// **'No bio added yet.'**
  String get noBioAdded;

  /// No description provided for @rating0.
  ///
  /// In en, this message translates to:
  /// **'0 rating'**
  String get rating0;

  /// No description provided for @accountInfoTitle.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInfoTitle;

  /// No description provided for @personalInfoTab.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInfoTab;

  /// No description provided for @securityTab.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get securityTab;

  /// No description provided for @statisticsTab.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statisticsTab;

  /// No description provided for @noInfoAvailable.
  ///
  /// In en, this message translates to:
  /// **'No information available'**
  String get noInfoAvailable;

  /// No description provided for @noSecuritySettingsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No security settings available'**
  String get noSecuritySettingsAvailable;

  /// No description provided for @noStatisticsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No statistics available'**
  String get noStatisticsAvailable;

  /// No description provided for @editPersonalInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit Personal Information'**
  String get editPersonalInfo;

  /// No description provided for @exportAccountData.
  ///
  /// In en, this message translates to:
  /// **'Export Account Data'**
  String get exportAccountData;

  /// No description provided for @activeSessions.
  ///
  /// In en, this message translates to:
  /// **'Active Sessions'**
  String get activeSessions;

  /// No description provided for @recentActivities.
  ///
  /// In en, this message translates to:
  /// **'Recent Activities'**
  String get recentActivities;

  /// No description provided for @accountStatistics.
  ///
  /// In en, this message translates to:
  /// **'Account Statistics'**
  String get accountStatistics;

  /// No description provided for @memberSinceLabel.
  ///
  /// In en, this message translates to:
  /// **'Member Since'**
  String get memberSinceLabel;

  /// No description provided for @lastLogin.
  ///
  /// In en, this message translates to:
  /// **'Last Login'**
  String get lastLogin;

  /// No description provided for @totalBookingsLabel.
  ///
  /// In en, this message translates to:
  /// **'Total Bookings'**
  String get totalBookingsLabel;

  /// No description provided for @totalReviewsLabel.
  ///
  /// In en, this message translates to:
  /// **'Total Reviews'**
  String get totalReviewsLabel;

  /// No description provided for @verifiedAccount.
  ///
  /// In en, this message translates to:
  /// **'Verified Account'**
  String get verifiedAccount;

  /// No description provided for @unverifiedAccount.
  ///
  /// In en, this message translates to:
  /// **'Unverified Account'**
  String get unverifiedAccount;

  /// No description provided for @currentSession.
  ///
  /// In en, this message translates to:
  /// **'Current'**
  String get currentSession;

  /// No description provided for @dataExportedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Data Exported Successfully'**
  String get dataExportedSuccessfully;

  /// No description provided for @dataExportSuccessMessage.
  ///
  /// In en, this message translates to:
  /// **'Your data has been exported successfully'**
  String get dataExportSuccessMessage;

  /// No description provided for @bookingsCount.
  ///
  /// In en, this message translates to:
  /// **'booking'**
  String get bookingsCount;

  /// No description provided for @completePersonalInfo.
  ///
  /// In en, this message translates to:
  /// **'Complete personal information'**
  String get completePersonalInfo;

  /// No description provided for @cannotLoadUserData.
  ///
  /// In en, this message translates to:
  /// **'Cannot load user data. Please try again.'**
  String get cannotLoadUserData;

  /// No description provided for @changePasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePasswordTitle;

  /// No description provided for @currentPassword.
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get currentPassword;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @confirmNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// No description provided for @enterCurrentPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter current password'**
  String get enterCurrentPassword;

  /// No description provided for @enterNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter new password'**
  String get enterNewPassword;

  /// No description provided for @passwordMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters'**
  String get passwordMinLength;

  /// No description provided for @confirmNewPasswordField.
  ///
  /// In en, this message translates to:
  /// **'Please confirm new password'**
  String get confirmNewPasswordField;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @changePasswordButton.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePasswordButton;

  /// No description provided for @passwordChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Password changed successfully'**
  String get passwordChangedSuccessfully;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @hostingResources.
  ///
  /// In en, this message translates to:
  /// **'Hosting Resources'**
  String get hostingResources;

  /// No description provided for @findCoHost.
  ///
  /// In en, this message translates to:
  /// **'Find Co-Host'**
  String get findCoHost;

  /// No description provided for @createNewListing.
  ///
  /// In en, this message translates to:
  /// **'Create New Listing'**
  String get createNewListing;

  /// No description provided for @privacySettingsTitle.
  ///
  /// In en, this message translates to:
  /// **'Privacy Settings'**
  String get privacySettingsTitle;

  /// No description provided for @profilePrivacy.
  ///
  /// In en, this message translates to:
  /// **'Profile Privacy'**
  String get profilePrivacy;

  /// No description provided for @showProfile.
  ///
  /// In en, this message translates to:
  /// **'Show Profile'**
  String get showProfile;

  /// No description provided for @showProfileSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Allow others to see your profile'**
  String get showProfileSubtitle;

  /// No description provided for @showEmail.
  ///
  /// In en, this message translates to:
  /// **'Show Email'**
  String get showEmail;

  /// No description provided for @showEmailSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Display your email in profile'**
  String get showEmailSubtitle;

  /// No description provided for @showPhone.
  ///
  /// In en, this message translates to:
  /// **'Show Phone Number'**
  String get showPhone;

  /// No description provided for @showPhoneSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Display your phone number in profile'**
  String get showPhoneSubtitle;

  /// No description provided for @dataAndLocation.
  ///
  /// In en, this message translates to:
  /// **'Data & Location'**
  String get dataAndLocation;

  /// No description provided for @shareLocation.
  ///
  /// In en, this message translates to:
  /// **'Share Location'**
  String get shareLocation;

  /// No description provided for @shareLocationSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Allow app to access your location'**
  String get shareLocationSubtitle;

  /// No description provided for @analyticsDataCollection.
  ///
  /// In en, this message translates to:
  /// **'Analytics Data Collection'**
  String get analyticsDataCollection;

  /// No description provided for @analyticsDataCollectionSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Help us improve the app with anonymous data'**
  String get analyticsDataCollectionSubtitle;

  /// No description provided for @downloadMyData.
  ///
  /// In en, this message translates to:
  /// **'Download My Data'**
  String get downloadMyData;

  /// No description provided for @downloadMyDataSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Get a copy of all your data'**
  String get downloadMyDataSubtitle;

  /// No description provided for @communication.
  ///
  /// In en, this message translates to:
  /// **'Communication'**
  String get communication;

  /// No description provided for @allowMessages.
  ///
  /// In en, this message translates to:
  /// **'Allow Messages'**
  String get allowMessages;

  /// No description provided for @allowMessagesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Allow hosts and guests to send you messages'**
  String get allowMessagesSubtitle;

  /// No description provided for @pushNotificationsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Receive instant notifications for messages and bookings'**
  String get pushNotificationsSubtitle;

  /// No description provided for @marketingEmails.
  ///
  /// In en, this message translates to:
  /// **'Marketing Emails'**
  String get marketingEmails;

  /// No description provided for @marketingEmailsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Receive email messages about offers and news'**
  String get marketingEmailsSubtitle;

  /// No description provided for @dataManagement.
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// No description provided for @clearSearchHistory.
  ///
  /// In en, this message translates to:
  /// **'Clear Search History'**
  String get clearSearchHistory;

  /// No description provided for @clearSearchHistorySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Delete all previous searches'**
  String get clearSearchHistorySubtitle;

  /// No description provided for @clearCache.
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get clearCache;

  /// No description provided for @clearCacheSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Delete temporary files and saved images'**
  String get clearCacheSubtitle;

  /// No description provided for @blockedUsers.
  ///
  /// In en, this message translates to:
  /// **'Blocked Users'**
  String get blockedUsers;

  /// No description provided for @blockedUsersSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Manage blocked users list'**
  String get blockedUsersSubtitle;

  /// No description provided for @downloadData.
  ///
  /// In en, this message translates to:
  /// **'Download Data'**
  String get downloadData;

  /// No description provided for @downloadDataMessage.
  ///
  /// In en, this message translates to:
  /// **'A copy of all your data will be sent to your email within 24 hours.'**
  String get downloadDataMessage;

  /// No description provided for @downloadDataSuccess.
  ///
  /// In en, this message translates to:
  /// **'Data download request successful'**
  String get downloadDataSuccess;

  /// No description provided for @clearSearchHistoryTitle.
  ///
  /// In en, this message translates to:
  /// **'Clear Search History'**
  String get clearSearchHistoryTitle;

  /// No description provided for @clearSearchHistoryMessage.
  ///
  /// In en, this message translates to:
  /// **'Do you want to delete all previous searches?'**
  String get clearSearchHistoryMessage;

  /// No description provided for @searchHistoryCleared.
  ///
  /// In en, this message translates to:
  /// **'Search history cleared'**
  String get searchHistoryCleared;

  /// No description provided for @clearCacheTitle.
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get clearCacheTitle;

  /// No description provided for @clearCacheMessage.
  ///
  /// In en, this message translates to:
  /// **'Do you want to delete all temporary files?'**
  String get clearCacheMessage;

  /// No description provided for @cacheCleared.
  ///
  /// In en, this message translates to:
  /// **'Cache cleared'**
  String get cacheCleared;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @logoutSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Logout Successful'**
  String get logoutSuccessful;

  /// No description provided for @chooseWhatToDoNow.
  ///
  /// In en, this message translates to:
  /// **'Choose what to do now'**
  String get chooseWhatToDoNow;

  /// No description provided for @reviewsLoadError.
  ///
  /// In en, this message translates to:
  /// **'Failed to load reviews'**
  String get reviewsLoadError;

  /// No description provided for @highestRated.
  ///
  /// In en, this message translates to:
  /// **'Highest Rated'**
  String get highestRated;

  /// No description provided for @lowestRated.
  ///
  /// In en, this message translates to:
  /// **'Lowest Rated'**
  String get lowestRated;

  /// No description provided for @minRating.
  ///
  /// In en, this message translates to:
  /// **'Minimum Rating'**
  String get minRating;

  /// No description provided for @fourPlusStars.
  ///
  /// In en, this message translates to:
  /// **'4+ stars'**
  String get fourPlusStars;

  /// No description provided for @threePlusStars.
  ///
  /// In en, this message translates to:
  /// **'3+ stars'**
  String get threePlusStars;

  /// No description provided for @twoPlusStars.
  ///
  /// In en, this message translates to:
  /// **'2+ stars'**
  String get twoPlusStars;

  /// No description provided for @onePlusStars.
  ///
  /// In en, this message translates to:
  /// **'1+ stars'**
  String get onePlusStars;

  /// No description provided for @noReviews.
  ///
  /// In en, this message translates to:
  /// **'No reviews'**
  String get noReviews;

  /// No description provided for @beFirstToReview.
  ///
  /// In en, this message translates to:
  /// **'Be the first to review this property'**
  String get beFirstToReview;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return SAr();
    case 'en':
      return SEn();
  }

  throw FlutterError(
      'S.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
